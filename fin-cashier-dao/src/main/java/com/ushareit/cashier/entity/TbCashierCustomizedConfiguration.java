package com.ushareit.cashier.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@TableName(value = "tb_cashier_customized_configuration")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class TbCashierCustomizedConfiguration implements Serializable {

    private static final long serialVersionUID = 1496086572839184822L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 配置code
     */
    @TableField(value = "config_code")
    private String configCode;

    /**
     * 场景code
     */
    @TableField(value = "scene_code")
    private String sceneCode;

    /**
     * 配置类型
     */
    @TableField(value = "config_type")
    private String configType;

    /**
     * 目标机构编码
     */
    @TableField(value = "target_org_no")
    private String targetOrgNo;

    /**
     * 卡组织
     */
    @TableField(value = "card_org")
    private String cardOrg;

    /**
     * 支付方式编号
     */
    @TableField(value = "payment_method_no")
    private String paymentMethodNo;
    /**
     * option表的displayCode
     */
    @TableField(value = "display_code")
    private String displayCode;
    /**
     * 印尼流量
     */
    @TableField(value = "idc_region")
    private String idcRegion;
    /**
     * 扩展 区分字段
     */
    @TableField(value = "extend_column")
    private String extendColumn;
    /**
     * 扩展 区分字段 值
     */
    @TableField(value = "extend_column_value")
    private String extendColumnValue;

    /**
     * 国家：CountryEnum
     */
    private String country;

    /**
     * 币种：CurrencyEnum
     */
    private String currency;

    @TableField(value = "merchant_id")
    private String merchantId;

    /**
     * 规则执行器
     */
    @TableField(value = "bean_name")
    private String beanName;

    /**
     * 配置等级
     */
    private int level;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否可用【0：不可用】 、【1：可用】
     */
    private int status;

    /**
     * 逻辑删除【0：未删除】、【1：已删除】
     */
    @TableField(value = "is_deleted")
    private int isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新时间
     */
    @TableField(value = "modified_time")
    private Date modifiedTime;

    /**
     * 更新人
     */
    private String modifier;

    /**
     * 配置内容
     */
    private String content;
}