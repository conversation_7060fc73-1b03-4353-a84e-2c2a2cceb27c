update fintech_cashier.tb_cashier_middlepage set param_json = '{"urlParams":[{"name":"dueDate","pattern":"CST"}],"barCodeStrategy":{"codeType":2,"returnType":2,"expireTime":172800,"barCodeSize":3,"height":10,"fontSize":2,"refreshByApi":true,"beanName":"BAR_QR_CODE_refresh"}}' where 
country = 'TW'
and payment_method_type = 'BarCode'
and target_org in ('FAMILYMART','7-ELEVEN','OKMART','HILIFE');

INSERT INTO `fintech_cashier`.`tb_cashier_middlepage` (`country`, `currency`, `product_code`, `merchant_id`, `payment_method_type`, `target_org`, `extend_column`, `extend_column_value`, `page_type`, `page_url`, `page_desc`, `cache_time`, `param_json`, `show_status`, `created_by`, `created_time`, `modified_by`, `modified_time`, `is_deleted`, `deleted_by`, `deleted_time`, `reserved1`, `reserved2`, `reserved3`) VALUES ('TH', 'THB', '/', '/', 'VirtualAccount', 'SCB_TH', '/', '/', '/', '/link/scb', 'SCB', 86400, '{\"urlParams\":[{\"name\":\"dueDate\",\"pattern\":\"9,yyyy-MM-dd HH:mm,GMT+7\"}],\"barCodeStrategy\":{\"beanName\":\"BAR_QR_CODE_SCB\",\"extInfo\":{\"qrWidth\":200,\"qrHeight\":200,\"qrFrontSize\":0,\"barWidth\":0,\"barHeight\":0,\"barFrontSize\":0,\"DPI\":200}}}', 1, 'system', '2023-05-10 08:49:27', 'system', '2023-05-12 08:40:57', 0, 'system', '2023-05-10 08:49:27', NULL, NULL, NULL);
INSERT INTO `fintech_cashier`.`tb_cashier_pay_option` (`display_code`, `country`, `currency`, `product_code`, `display_name`, `display_desc`, `display_logo`, `display_level`, `parent_display_code`, `assign_payment_parent_display_code`, `payment_method_type`, `target_org`, `card_org`, `child_size`, `display_sort`, `show_status`, `extend_column`, `extend_column_value`, `payment_combine_code`, `created_by`, `created_time`, `modified_by`, `modified_time`, `is_deleted`, `deleted_by`, `deleted_time`, `channel_logo`, `payment_method_no`, `cashier_product_name`, `target_org_name`, `reserved1`, `reserved2`, `reserved3`, `reserved4`, `reserved5`) VALUES ('***************************', 'TH', 'THB', '/', 'Cross Bank Bill Payment', 'Supports both corporate and personal account payments ', 'https://img-cdn.payermax.com/payWayImg/C0_Bank_Transfer.png', 2, 'NA', 'NA', 'VirtualAccount', 'SCB_TH', '/', 0, 6, 1, '', '', '', 'system', '2023-05-09 10:57:29', 'system', '2023-05-09 12:46:02', 0, 'system', '2023-05-09 10:57:29', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `fintech_cashier`.`tb_cashier_target_org` (`target_org_no`, `target_org_name`, `card_org`, `card_org_name`, `payment_method_no`, `country`, `currency`, `limit_config`, `expire_time`, `is_support_refund`, `column_mapping`, `remark`, `status`, `create_time`, `creator`, `modified_time`, `modifier`, `is_deleted`, `is_partial_refund`, `payment_type`, `logo`, `tokenization`, `fee_bearer`) VALUES ('SCB_TH', 'Cross Bank Bill Payment', NULL, NULL, 'VIRTUAL_ACCOUNT', 'TH', 'THB', '[{\"type\": \"AMOUNT_RANGE\",\"value\": \"1-2000000\",\"currency\":\"THB\"}]', '', 0, NULL, '', 1, '2023-05-09 12:50:08', 'system', '2023-05-09 12:51:34', 'system', 0, 0, 20, 'https://img-cdn.payermax.com/payWayImg/C0_Bank_Transfer.png', 0, '');