INSERT INTO `fintech_cashier`.`tb_cashier_customized_configuration` (`config_code`, `scene_code`, `config_type`, `target_org_no`, `card_org`, `payment_method_no`, `country`, `currency`, `bean_name`, `merchant_id`, `content`, `level`, `remark`, `status`, `is_deleted`, `create_time`, `creator`, `modified_time`, `modifier`) VALUES ('DP1655400047891845320', 'process_page_poll', 'POLL_CASHIER_1_0_COMMON', '', '', '', '', '', 'assemblePopUp', '', '{\"closeRedirectUrlKey\":\"\",\"popUpKey\":\"popUp\",\"maskRedirectUrlKey\":\"\",\"leftButtonRedirectUrlKey\":\"\",\"rightButtonRedirectUrlKey\":\"doResultPageAction\",\"redirectTypeKey\":\"4\",\"redirectUrlKey\":\"doResultPageAction\",\"popUpConfig\":\"{\\\"title\\\":{},\\\"content\\\":{},\\\"close\\\":{},\\\"leftButton\\\":{},\\\"mask\\\":{},\\\"rightButton\\\":{\\\"key\\\":\\\"POLL_RIGHT_BUTTON_TOAST_DEFAULT_KEY\\\"},\\\"type\\\":\\\"toast\\\"}\"}', 1, '收银台轮循模板', 1, 0, '2023-05-08 02:36:13', 'system', '2023-05-30 08:43:34', 'system');
INSERT INTO `fintech_cashier`.`tb_cashier_customized_configuration` (`config_code`, `scene_code`, `config_type`, `target_org_no`, `card_org`, `payment_method_no`, `country`, `currency`, `bean_name`, `merchant_id`, `content`, `level`, `remark`, `status`, `is_deleted`, `create_time`, `creator`, `modified_time`, `modifier`) VALUES ('DP1655400047891845332', 'process_page_poll', 'PAYMENT_FAILED_The user has cancelled to pay.', '', '', '', '', '', 'assemblePopUp', '', '{\"closeRedirectUrlKey\":\"\",\"popUpKey\":\"popUp\",\"maskRedirectUrlKey\":\"\",\"leftButtonRedirectUrlKey\":\"doResultPageAction\",\"rightButtonRedirectUrlKey\":\"doChangeMethodAction\",\"redirectTypeKey\":\"4\",\"redirectUrlKey\":\"\",\"popUpConfig\":\"{\\\"title\\\": {\\\"key\\\": \\\"modal.CANCELED_IN_OVO.title\\\"},\\\"content\\\": {\\\"key\\\": \\\"modal.CANCELED_IN_OVO.content\\\"},\\\"close\\\": {},\\\"leftButton\\\": {\\\"key\\\": \\\"modal.CANCELED_IN_OVO.cancel\\\"},\\\"mask\\\": {},\\\"rightButton\\\": {\\\"key\\\": \\\"modal.CANCELED_IN_OVO.ok\\\"},\\\"type\\\": \\\"modal\\\"}\"}', 1, '收银台轮循模板', 1, 0, '2023-05-08 02:36:13', 'system', '2023-06-01 07:07:42', 'system');
INSERT INTO `fintech_cashier`.`tb_cashier_customized_configuration` (`config_code`, `scene_code`, `config_type`, `target_org_no`, `card_org`, `payment_method_no`, `country`, `currency`, `bean_name`, `merchant_id`, `content`, `level`, `remark`, `status`, `is_deleted`, `create_time`, `creator`, `modified_time`, `modifier`) VALUES ('*********************', 'process_page_poll', 'INVALID_MOBILE_NUMBER_Your phone number is invalid, or your account is not active, please confirm and re-enter.', '', '', '', '', '', 'assemblePopUp', '', '{\"closeRedirectUrlKey\":\"\",\"popUpKey\":\"popUp\",\"maskRedirectUrlKey\":\"\",\"leftButtonRedirectUrlKey\":\"doChangeMethodAction\",\"rightButtonRedirectUrlKey\":\"doEditMobilePageAction\",\"redirectTypeKey\":\"4\",\"redirectUrlKey\":\"\",\"popUpConfig\":\"{\\\"title\\\": {\\\"key\\\": \\\"modal.NOT_OVO_ACCOUNT.title\\\"},\\\"content\\\": {\\\"key\\\": \\\"modal.NOT_OVO_ACCOUNT.content\\\"},\\\"close\\\": {},\\\"leftButton\\\": {\\\"key\\\": \\\"modal.NOT_OVO_ACCOUNT.cancel\\\"},\\\"mask\\\": {},\\\"rightButton\\\": {\\\"key\\\": \\\"modal.NOT_OVO_ACCOUNT.ok\\\"},\\\"type\\\": \\\"modal\\\"}\"}', 1, '收银台轮循模板', 1, 0, '2023-05-08 02:36:13', 'system', '2023-06-01 07:08:29', 'system');
INSERT INTO `fintech_cashier`.`tb_cashier_customized_configuration` (`config_code`, `scene_code`, `config_type`, `target_org_no`, `card_org`, `payment_method_no`, `country`, `currency`, `bean_name`, `merchant_id`, `content`, `level`, `remark`, `status`, `is_deleted`, `create_time`, `creator`, `modified_time`, `modifier`) VALUES ('*********************', 'process_page_poll', 'AMOUNT_LIMIT_The amount exceeds exceeds user amount limit.', '', '', '', '', '', 'assemblePopUp', '', '{\"closeRedirectUrlKey\":\"\",\"popUpKey\":\"popUp\",\"maskRedirectUrlKey\":\"\",\"leftButtonRedirectUrlKey\":\"doResultPageAction\",\"rightButtonRedirectUrlKey\":\"doChangeMethodAction\",\"redirectTypeKey\":\"4\",\"redirectUrlKey\":\"\",\"popUpConfig\":\"{\\\"title\\\": {\\\"key\\\": \\\"modal.OVO_LIMIT_EXCEEDED.title\\\"},\\\"content\\\": {\\\"key\\\": \\\"modal.OVO_LIMIT_EXCEEDED.content\\\"},\\\"close\\\": {},\\\"leftButton\\\": {\\\"key\\\": \\\"modal.OVO_LIMIT_EXCEEDED.cancel\\\"},\\\"mask\\\": {},\\\"rightButton\\\": {\\\"key\\\": \\\"modal.OVO_LIMIT_EXCEEDED.ok\\\"},\\\"type\\\": \\\"modal\\\"}\"}', 1, '收银台轮循模板', 1, 0, '2023-05-08 02:36:13', 'system', '2023-06-01 07:09:14', 'system');
INSERT INTO `fintech_cashier`.`tb_cashier_customized_configuration` (`config_code`, `scene_code`, `config_type`, `target_org_no`, `card_org`, `payment_method_no`, `country`, `currency`, `bean_name`, `merchant_id`, `content`, `level`, `remark`, `status`, `is_deleted`, `create_time`, `creator`, `modified_time`, `modifier`) VALUES ('DP1655400047811868976', 'process_page_poll', 'default', '', '', '', '', '', 'assemblePopUp', '', '{\"closeRedirectUrlKey\":\"\",\"popUpKey\":\"noAction\",\"maskRedirectUrlKey\":\"\",\"leftButtonRedirectUrlKey\":\"\",\"rightButtonRedirectUrlKey\":\"\",\"redirectTypeKey\":\"1\",\"redirectUrlKey\":\"doResultPageAction\",\"popUpConfig\":\"{\\\"title\\\": {},\\\"content\\\": {},\\\"close\\\": {},\\\"leftButton\\\": {},\\\"mask\\\": {},\\\"rightButton\\\": {\\\"key\\\": \\\"POLL_RIGHT_BUTTON_TOAST_DEFAULT_KEY\\\"},\\\"type\\\": \\\"toast\\\"}\"}', 1, '收银台轮循模板', 1, 0, '2023-05-08 02:36:13', 'system', '2023-06-01 07:09:53', 'system');