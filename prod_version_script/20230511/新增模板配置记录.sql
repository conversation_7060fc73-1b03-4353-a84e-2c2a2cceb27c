INSERT INTO `fintech_cashier`.`tb_cashier_customized_configuration` (`config_code`, `scene_code`, `config_type`, `target_org_no`, `card_org`, `payment_method_no`, `country`, `currency`, `bean_name`, `merchant_id`, `content`, `level`, `remark`, `status`, `is_deleted`, `create_time`, `creator`, `modified_time`, `modifier`) VALUES ('DP1654697139189714944', 'guid_page', 'page_template', 'OVO', '', 'WALLET', '', '', 'doResultPageAction,doCountDownTimeAction,doOvoBuyerPhoneNumberAction', '', '{\"timeType\":\"countDown\",\"timeConfig\":{\"key\":\"countDown\",\"redirectUrl\":\"%s\",\"time\":%s},\"poll\":true,\"template\":\"[{\\\"cardType\\\":\\\"normal\\\",\\\"componentType\\\":\\\"Tile\\\",\\\"componentData\\\":[{\\\"contentType\\\":\\\"Title\\\",\\\"content\\\":{\\\"key\\\":\\\"ovo.tileTitle\\\"}},{\\\"contentType\\\":\\\"Desc\\\",\\\"content\\\":[{\\\"key\\\":\\\"ovo.tileSent\\\",\\\"var\\\":{\\\"phoneNumber\\\":\\\"{{phoneNumber}}\\\"}},{\\\"key\\\":\\\"ovo.tileDesc\\\"}]},{\\\"contentType\\\":\\\"Image\\\",\\\"content\\\":{\\\"url\\\":\\\"https://img-cdn.payermax.com/icon/PayInfo/OVO_PayInfo_Steps_default.jpg\\\"}}]},{\\\"cardType\\\":\\\"fold\\\",\\\"foldLabel\\\":{\\\"key\\\":\\\"ovo.foldLabel\\\"},\\\"componentType\\\":\\\"Step\\\",\\\"componentData\\\":[{\\\"label\\\":{\\\"key\\\":\\\"ovo.step1\\\"},\\\"contentType\\\":\\\"Image\\\",\\\"content\\\":{\\\"url\\\":\\\"https://img-cdn.payermax.com/icon/PayInfo/OVO_PayInfo_Steps_1.jpg\\\"}},{\\\"label\\\":{\\\"key\\\":\\\"ovo.step2\\\"},\\\"contentType\\\":\\\"Image\\\",\\\"content\\\":{\\\"url\\\":\\\"https://img-cdn.payermax.com/icon/PayInfo/OVO_PayInfo_Steps_2.jpg\\\"}},{\\\"label\\\":{\\\"key\\\":\\\"ovo.step3\\\"},\\\"contentType\\\":\\\"Image\\\",\\\"content\\\":{\\\"url\\\":\\\"https://img-cdn.payermax.com/icon/PayInfo/OVO_PayInfo_Steps_3.jpg\\\"}}]}]\",\"templateVars\":\"{\\\"phoneNumber\\\":\\\"%s\\\"}\"}', 1, '', 1, 0, '2023-05-06 05:48:28', 'system', '2023-05-08 03:59:52', 'system');