 update tb_cashier_pay_option set reserved1 ='{\"buyerPhoneNo\":{\"validators\":[{\"checker\":\"^08\",\"errorCode\":\"PARAM_START_WITH_08\",\"errorTip\":\"Mobile should start with 08\"},{\"checker\":\"^08[0-9]{7,11}$\",\"initTipCode\":\"PARAM_PHONE_SEEM_INVALID\",\"initTipMsg\":\"Seems the phone is invalid,please check\"}]}}',reserved2 = '{\"buyerPhoneNo\":{\"apiKey\":\"buyerPhoneNo\",\"label\":\"Phone Number\",\"labelKey\":\"phoneNumber\",\"maxlength\":\"13\",\"limiter\":\"/^[0-9]+$/\",\"filter\":\"/^[0-9]/g\",\"type\":\"tel\",\"autocomplete\":\"tel\",\"inputmode\":\"tel\",\"validators\":[{\"checker\":\"/^08/\",\"errorCode\":\"PARAM_START_WITH_08\",\"errorTip\":\"Mobile number should start with 08\"},{\"checker\":\"/^.{9,13}$/\",\"errorCode\":\"PARAM_MIN_LENGTH_9\",\"errorTip\":\"Required 9 to 13 digits\"}]}}' where display_code = 'CS20201203221407723288416';