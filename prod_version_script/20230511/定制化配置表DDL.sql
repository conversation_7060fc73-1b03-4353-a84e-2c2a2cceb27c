CREATE TABLE `tb_cashier_customized_configuration` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `config_code` varchar(128)  NOT NULL DEFAULT '' COMMENT '配置code',
  `scene_code` varchar(64)  NOT NULL DEFAULT '' COMMENT '场景code',
  `config_type` varchar(64)  NOT NULL DEFAULT '' COMMENT '配置类型',
  `target_org_no` varchar(128)  DEFAULT '' COMMENT '目标机构编码',
  `card_org` varchar(16)  DEFAULT '' COMMENT '卡组织',
  `payment_method_no` varchar(64)  DEFAULT '' COMMENT '支付方式编号',
  `country` varchar(32)  DEFAULT '' COMMENT '国家：CountryEnum',
  `currency` varchar(32)  DEFAULT '' COMMENT '币种：CurrencyEnum',
  `bean_name` varchar(256) DEFAULT '' COMMENT '规则执行器多个 使用逗号隔开',
  `merchant_id` varchar(255) DEFAULT '' COMMENT '商户号',
  `content` text COMMENT '配置内容',
  `level` tinyint(4) NOT NULL DEFAULT '1' COMMENT '配置等级',
  `remark` varchar(256)  DEFAULT '' COMMENT '备注',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否可用【0：不可用】 、【1：可用】',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '逻辑删除【0：未删除】、【1：已删除】',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` varchar(32)  NOT NULL DEFAULT 'system' COMMENT '创建人',
  `modified_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `modifier` varchar(32)  NOT NULL DEFAULT 'system' COMMENT '更新人',
                PRIMARY KEY (`id`),
                UNIQUE KEY `uq_config_code` (`config_code`) USING BTREE COMMENT '唯一code',
                KEY `idx_scene_type` (`scene_code`,`config_type`) USING BTREE COMMENT '联合索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;