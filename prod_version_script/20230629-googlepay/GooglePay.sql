INSERT INTO fintech_cashier.tb_cashier_pay_option (display_code,country,currency,product_code,display_name,display_desc,display_logo,display_level,parent_display_code,assign_payment_parent_display_code,payment_method_type,target_org,card_org,child_size,display_sort,show_status,extend_column,extend_column_value,payment_combine_code,created_by,created_time,modified_by,modified_time,is_deleted,deleted_by,deleted_time,channel_logo,payment_method_no,cashier_product_name,target_org_name,reserved1,reserved2,reserved3,reserved4,reserved5) VALUES
	 ('CS202306161338HKVisa','HK','HKD','/','Google Pay','','https://img-cdn.payermax.com/payWayImg/Google_Pay_Global.png',2,'','','GooglePay','*','VISA',0,1,1,'/','/','GooglePay_DIRECT','wangpeng2','2023-06-16 07:43:25','wangpeng2','2023-06-16 12:14:19',0,'wangpeng2','2023-06-16 07:43:25','','','','Google Pay','','','','','');
INSERT INTO fintech_cashier.tb_cashier_pay_option (display_code,country,currency,product_code,display_name,display_desc,display_logo,display_level,parent_display_code,assign_payment_parent_display_code,payment_method_type,target_org,card_org,child_size,display_sort,show_status,extend_column,extend_column_value,payment_combine_code,created_by,created_time,modified_by,modified_time,is_deleted,deleted_by,deleted_time,channel_logo,payment_method_no,cashier_product_name,target_org_name,reserved1,reserved2,reserved3,reserved4,reserved5) VALUES
	 ('CS202306161338HKMast','HK','HKD','/','Google Pay','','https://img-cdn.payermax.com/payWayImg/Google_Pay_Global.png',2,'','','GooglePay','*','MASTERCARD',0,1,1,'/','/','GooglePay_DIRECT','wangpeng2','2023-06-16 07:43:25','wangpeng2','2023-06-16 06:04:29',0,'wangpeng2','2023-06-16 07:43:25','','','','Google Pay','','','','','');
