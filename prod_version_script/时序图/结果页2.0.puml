@startuml
'https://plantuml.com/sequence-diagram

autonumber
title 收银台2.0结果页时序图
activate 收银台
订单中心->订单中心:下单
订单中心->收银核心:获取首页地址
收银核心->收银核心:根据【商户号+国家】维度拼接首页URL，\n同时将收银台版本存入redis
收银核心 --> 订单中心: 返回收银台首页URL

收银台 -> 收银核心: 获取结果页
收银核心 -> 收银核心: 从redis获取收银台版本，根据收银台版本\n组装结果页URL\n走结果页2.0需要满足下面条件：\n收银台版本为2.0+灰度商户+灰度国家
收银核心 --> 收银台: 返回结果页URL


收银台->收银台:打开结果页2.0接口：queryReceivables
收银台->订单中心:查询支付信息
收银台->商户服务:查询商户信息
收银台->凭证服务:取出目标机构等信息
收银台->上下文:取出渠道侧信息
收银台->收银台:组装结果页信息
@enduml

