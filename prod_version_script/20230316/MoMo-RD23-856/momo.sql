INSERT INTO fintech_cashier.tb_cashier_pay_option
(display_code, country, currency, product_code, display_name, display_desc, display_logo, display_level, parent_display_code, assign_payment_parent_display_code, payment_method_type, target_org, card_org, child_size, display_sort, show_status, extend_column, extend_column_value, payment_combine_code, created_by, created_time, modified_by, modified_time, is_deleted, deleted_by, deleted_time, channel_logo, payment_method_no, cashier_product_name, target_org_name, reserved1, reserved2, reserved3, reserved4, reserved5)
VALUES('CS20230309152253664146460678', 'VN', 'VND', '/', 'MoMo', 'Scan to pay via MoMo e-wallet app', 'https://img-cdn.payermax.com/payWayImg/Viet_MOMO_VN.png', 0, 'NA', 'NA', 'Wallet', 'MOMO', '/', 0, 3, 1, '/', '/', 'Wallet_MOMO', 'system', '2023-03-09 02:17:04', 'system', '2023-03-09 08:49:30', 0, 'system', '2023-03-09 02:17:04', '', '5', '/', '/', '', '', '', '', '');


INSERT INTO fintech_cashier.tb_cashier_middlepage
(country, currency, product_code, merchant_id, payment_method_type, target_org, extend_column, extend_column_value, page_type, page_url, page_desc, cache_time, param_json, show_status, created_by, created_time, modified_by, modified_time, is_deleted, deleted_by, deleted_time, reserved1, reserved2, reserved3)
VALUES('VN', 'VND', '/', '/', 'WALLET', 'MOMO', '/', '/', '/', '/link/momo', 'MOMO', 86400, '{"urlParams":[{"name":"dueDate","pattern":"8,yyyy-MM-dd HH:mm,GMT+7"}],"barCodeStrategy":{"codeType":1,"returnType":1,"expireTime":604800,"manualExpirationFlag":true}}', 1, 'system', '2023-03-09 03:20:59', 'system', '2023-03-09 02:52:42', 0, 'system', '2023-03-09 03:20:59', NULL, NULL, NULL);
