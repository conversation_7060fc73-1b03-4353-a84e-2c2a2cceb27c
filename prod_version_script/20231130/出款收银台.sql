
 CREATE TABLE `tb_payout_payment_option_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `template_id` varchar(64) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '配置code',
  `merchant_id` varchar(128) DEFAULT '' COMMENT '商户号',
  `country` varchar(3) CHARACTER SET utf8 DEFAULT '' COMMENT '国家：CountryEnum',
  `payment_method_no` varchar(256) CHARACTER SET utf8 DEFAULT '' COMMENT '支付方式编号',
  `org_no` varchar(256) CHARACTER SET utf8 DEFAULT '' COMMENT '机构或卡组织编码',
  `content` text COMMENT '配置内容',
  `remark` varchar(256) CHARACTER SET utf8 DEFAULT '' COMMENT '备注',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否可用【0：不可用】 、【1：可用】',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` varchar(32) CHARACTER SET utf8 NOT NULL DEFAULT 'system' COMMENT '创建人',
  `modified_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `modifier` varchar(32) CHARACTER SET utf8 NOT NULL DEFAULT 'system' COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_template_id` (`template_id`) USING BTREE COMMENT '唯一code'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;


 CREATE TABLE `tb_payout_payment_params_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `template_id` varchar(64) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '配置code',
  `merchant_id` varchar(128) DEFAULT '' COMMENT '商户号',
  `country` varchar(3) CHARACTER SET utf8 DEFAULT '' COMMENT '国家：CountryEnum',
  `payment_method_no` varchar(256) CHARACTER SET utf8 DEFAULT '' COMMENT '支付方式编号',
  `org_no` varchar(256) CHARACTER SET utf8 DEFAULT '' COMMENT '机构或卡组织编码',
  `param_key` varchar(512) CHARACTER SET utf8 DEFAULT '' COMMENT '采集的支付要素名称',
  `content` text COMMENT '配置内容',
  `remark` varchar(256) CHARACTER SET utf8 DEFAULT '' COMMENT '备注',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否可用【0：不可用】 、【1：可用】',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` varchar(32) CHARACTER SET utf8 NOT NULL DEFAULT 'system' COMMENT '创建人',
  `modified_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `modifier` varchar(32) CHARACTER SET utf8 NOT NULL DEFAULT 'system' COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_template_id` (`template_id`) USING BTREE COMMENT '唯一code'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;