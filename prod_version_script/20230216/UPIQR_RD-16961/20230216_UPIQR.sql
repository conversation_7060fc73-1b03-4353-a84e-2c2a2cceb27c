
update fintech_cashier.tb_cashier_pay_option s set s.display_sort = 3 where s.display_code='CS20201204123949732735872';
update fintech_cashier.tb_cashier_pay_option s set s.display_sort = 4 where s.display_code='CS20201204124004753735872';
update fintech_cashier.tb_cashier_pay_option s set s.display_sort = 5 where s.display_code='CS20201204124023987335872';
update fintech_cashier.tb_cashier_pay_option s set s.display_sort = 7 where s.display_code='CS20201204124033751235872';
update fintech_cashier.tb_cashier_pay_option s set s.display_sort = 6 where s.display_code='CS20201204124044828335872';
INSERT INTO `fintech_cashier`.`tb_cashier_pay_option` (`display_code`, `country`, `currency`, `product_code`, `display_name`, `display_desc`, `display_logo`, `display_level`, `parent_display_code`, `assign_payment_parent_display_code`, `payment_method_type`, `target_org`, `card_org`, `child_size`, `display_sort`, `show_status`, `extend_column`, `extend_column_value`, `payment_combine_code`, `created_by`, `created_time`, `modified_by`, `modified_time`, `is_deleted`, `deleted_by`, `deleted_time`, `channel_logo`, `payment_method_no`, `cashier_product_name`, `target_org_name`, `reserved1`, `reserved2`, `reserved3`, `reserved4`, `reserved5`) VALUES ( 'CS20230216123541226665872', 'IN', 'INR', '/', 'UPI QR', '', 'https://img-cdn.payermax.com/payWayImg/UPI_IN.png', 2, 'NA', 'NA', 'Wallet', 'UPIQR', '/', 0, 2, 1, '/', '/', '/', 'system', '2023-02-07 16:43:25', 'system', '2023-02-07 13:40:24', 0, 'system', '2021-09-27 07:43:25', NULL, '4', 'WALLET UPI QR', 'UPIQR', '00', '', '', 'P00011', '00');
INSERT INTO `fintech_cashier`.`tb_cashier_middlepage` (`country`, `currency`, `product_code`, `merchant_id`, `payment_method_type`, `target_org`, `extend_column`, `extend_column_value`, `page_type`, `page_url`, `page_desc`, `cache_time`, `param_json`, `show_status`, `created_by`, `created_time`, `modified_by`, `modified_time`, `is_deleted`, `deleted_by`, `deleted_time`, `reserved1`, `reserved2`, `reserved3`) VALUES ('IN', 'INR', '/', '/', 'Wallet', 'UPIQR', '', '/', '/', '/link/upi', 'upi', 86400, '{\"urlParams\":[],\"barCodeStrategy\":{\"codeType\":1,\"returnType\":1,\"expireTime\":900}}', 1, 'system', '2023-02-09 13:24:18', 'system', '2023-02-09 12:24:29', 0, 'system', '2023-02-09 13:24:18', '', '', '');