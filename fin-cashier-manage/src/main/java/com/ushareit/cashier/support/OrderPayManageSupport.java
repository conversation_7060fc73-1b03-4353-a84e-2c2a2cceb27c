package com.ushareit.cashier.support;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.payermax.basic.contexcenter.service.client.enums.PaymentContextTypeEnum;
import com.payermax.basic.voucher.common.contract.PayStepPaymentScenePaymentInfo;
import com.payermax.basic.voucher.facade.response.GetPaymentVoucherResponse;
import com.payermax.card.security.api.response.CardBinDTO;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.fin.exchange.service.response.InquiryPaymentResponse;
import com.payermax.order.enums.OuterTradeOrderStatus;
import com.ushareit.cashier.constants.CommonConstants;
import com.ushareit.cashier.contextholder.CashierLanguageContextHolder;
import com.ushareit.cashier.contextholder.CashierPayRequestContextHolder;
import com.ushareit.cashier.convert.OrderPayRequestConvert;
import com.ushareit.cashier.convert.PayRequestConvert;
import com.ushareit.cashier.convert.TradeDetailConvert;
import com.ushareit.cashier.covert.ChannelResponseConvert;
import com.ushareit.cashier.domain.*;
import com.ushareit.cashier.dto.request.*;
import com.ushareit.cashier.dto.request.jwt.PaymentInquiryRequestV2Payloads;
import com.ushareit.cashier.dto.response.*;
import com.ushareit.cashier.entity.CashierPayOption;
import com.ushareit.cashier.enums.AccountErrorCodeTypeEnum;
import com.ushareit.cashier.enums.MultiLanguageEnum;
import com.ushareit.cashier.enums.PageOperateStatusEnum;
import com.ushareit.cashier.enums.ResultTypeEnum;
import com.ushareit.cashier.enums.errorcode.ErrorCodeEnum;
import com.ushareit.cashier.exception.BusinessException4ThrowOutData;
import com.ushareit.cashier.facade.CashierOrderPayFacade;
import com.ushareit.cashier.payment.enums.PaymentInstrument;
import com.ushareit.cashier.process.common.ChineseToPinyinHandle;
import com.ushareit.cashier.process.common.CommonPageService;
import com.ushareit.cashier.process.frame.StorageOperateStatusService;
import com.ushareit.cashier.reform.domain.PaymentDomainContext;
import com.ushareit.cashier.reform.facilities.CardBinFacilitiesService;
import com.ushareit.cashier.reform.facilities.DisplayDomainFacilitiesService;
import com.ushareit.cashier.reform.facilities.customized.handle.MoneyHandle;
import com.ushareit.cashier.reform.facilities.remote.CardRemoteService;
import com.ushareit.cashier.reform.payment.flow.PaymentFlowFacade;
import com.ushareit.cashier.reform.preservative.inbound.InboundPreservativeTransformer;
import com.ushareit.cashier.reform.preservative.inner.convert.PayloadsConvert;
import com.ushareit.cashier.reform.preservative.outbound.OutboundPreservativeTransformer;
import com.ushareit.cashier.service.*;
import com.ushareit.cashier.service.reform.CashierRequestNoCheckService;
import com.ushareit.cashier.support.payment.PayBaseManageSupport;
import com.ushareit.cashier.support.payment.route.PaymentManage4WalletAuthAdaptor;
import com.ushareit.cashier.support.uatCallback.UatPaymentCallbackManage;
import com.ushareit.cashier.utils.*;
import com.ushareit.fintech.base.api.dto.CardBinCountryDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/7/19 20:21
 */

@Slf4j
@Component
@DubboService(version = "1.0",retries = 0)
public class OrderPayManageSupport implements CashierOrderPayFacade{

    Map<String, Function<BusinessException,ProcessingPayCache>> accountHandleMappings = Maps.newHashMap();

    @Resource
    private OrderService orderService;
    @Resource
    private CashierDisplayManageSupport cashierDisplayManageSupport;
    @Resource
    private KnowPaymentInstanceService knowPaymentInstanceService;
    @Resource
    private PayRequestConvert payRequestConvert;
    @Resource
    private CommonBusinessService commonBusinessService;
    @Resource
    private MemberShipManageSupport memberShipManageSupport;
    @Resource
    private MappingErrorCodeService mappingErrorCodeService;
    @Resource
    private VoucherService voucherService;
    @Resource
    private BasicContextCacheService contextCacheService;
    @Resource
    private BizCodeMessageService bizCodeMessageService;
    @Resource
    private Map<String, UatPaymentCallbackManage> uatPaymentCallbackManageMap;
    @Resource
    private Map<String, PayBaseManageSupport> payManageSupportMap;
    @Value("${shareit.uat.callback.mock:true}")
    private boolean isUatCallback;
    @Resource
    private CashierCorePaymentManageSupport cashierCorePaymentManage;
    @Resource
    private CommonCacheService commonCacheService;
    @Resource
    private MerchantMemberManageSupport merchantMemberManage;
    @Resource
    private CashierOrderPayFacade cashierOrderPayFacade;
    @Resource
    private ChannelResponseConvert channelResponseConvert;
    @Resource
    private TradeDetailConvert tradeDetailConvert;
    @Resource
    private AuthManageSupport authManageSupport;
    @Resource
    private OrderPayRequestConvert orderPayRequestConvert;
	@Resource
    private CommonBusinessManageSupport commonBusinessManageSupport;

    @Resource
    private ChineseToPinyinHandle pinyinHandle;
    @Resource
    private CommonPageService commonPageService;
    @Resource
    private BehalfTaxService behalfTaxInitService;
    @Autowired
    private PaymentFlowFacade paymentFlowFacade;
    @Autowired
    private OutboundPreservativeTransformer outboundPreservativeService;
    @Autowired
    private InboundPreservativeTransformer inboundPreservativeTransformer;
    @Resource
    private PayManageSupport payManageSupport;
    @Resource
    private CashierRequestNoCheckService cashierRequestNoCheckService;
    @Resource
    private BusinessRuleService businessRuleService;
    @Resource
    private PayloadsConvert payloadsConvert;
    @Resource
    private ApolloValue apolloValue;
    @Resource
    private CardBinFacilitiesService cardBinFacilitiesService;
    @Resource
    private CardRemoteService cardRemoteService;
    @Resource
    private DisplayDomainFacilitiesService displayDomainFacilitiesService;
    @Resource
    private MoneyHandle                    moneyHandle;
    @Resource
    private PaymentManage4WalletAuthAdaptor paymentManage4WalletAuthAdaptor;
    @Resource
    private StorageOperateStatusService storageOperateStatusService;

    @PostConstruct
    public void accountHandleInit() {
        accountHandleMappings.put(AccountErrorCodeTypeEnum.INVALID_MOBILE_NUMBER.getCode(),(be)-> doPhoneAction(be));
        accountHandleMappings.put(AccountErrorCodeTypeEnum.EMAIL_INVALID.getCode(),(be)->doEmailAction(be));
        accountHandleMappings.put(AccountErrorCodeTypeEnum.ACCOUNT_INVALID.getCode(),(be)-> doAccountAction(be));
        accountHandleMappings.put(AccountErrorCodeTypeEnum.ACCOUNT_REGISTER_INVALID.getCode(),(be)-> doPhoneAction(be));
    }

    /**
     * 支付接口
     *
     * @param payRequest
     */
    @Override
    public Result<PayResponse> orderPay(PayRequest payRequest) {
        cashierRequestNoCheckService.checkOrderPayCashierRequestNo(payRequest);
        if (PaymentInstrument.GOOGLEPAY.getPaymentMode().equalsIgnoreCase(payRequest.getPaymentMode())) {
            Result<PaymentDomainContext> result = paymentFlowFacade.execute(inboundPreservativeTransformer.toPaymentDomainContext(payRequest));
            return outboundPreservativeService.toPayResponse(result);
        }
        OrderPayRequest orderPayRequest = orderPayRequestConvert.convertToOrderPayRequest(payRequest);
        PaymentResult paymentResult =payManageSupportMap.get(payRequest.getNextApiType()).orderPayExecute(orderPayRequest,false);
        PayResponse payResponse = CommonBusinessManageSupport.createSuccessOrFailedPayResponse(paymentResult);
        this.storageOperateStatus(payRequest,payResponse);
        return payResponse.isSuccess() ? ResultUtil.success(payResponse) : ResultUtil.fail(payResponse.getErrorCode(), payResponse.getErrorMsg(), payResponse);
    }

    @Override
    public Result<PayResponse> queryAndPay(PayRequest payRequest) {
        OrderPayRequest orderPayRequest = orderPayRequestConvert.convertToOrderPayRequest(payRequest);
        PaymentResult paymentResult = payManageSupport.queryAuthAndPay(orderPayRequest);
        PayResponse payResponse = CommonBusinessManageSupport.createSuccessOrFailedPayResponse(paymentResult);
        return payResponse.isSuccess() ? ResultUtil.success(payResponse) : ResultUtil.fail(payResponse.getErrorCode(), payResponse.getErrorMsg(), payResponse);
    }

    @Override
    public Result<ResultPageResponse> paymentInquiry(PaymentInquiryRequest paymentInquiryRequest) {
        String tradeToken = paymentInquiryRequest.getTradeToken();
        String cachePayRequestNo = commonCacheService.getAuthPayRequestNoCache(tradeToken, paymentInquiryRequest.getCountry());
        String payRequestNo = StringUtils.defaultIfBlank(paymentInquiryRequest.getPayRequestNo(),cachePayRequestNo);
        if(StringUtils.isAllBlank(payRequestNo,tradeToken)){
            log.error("orderService tradeQry param payRequestNo and tradeToken cant be all blank");
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR.getCode(),ErrorCodeEnum.INVALID_PARAMS.getEnName());
        }
        //0.设置前端语言
        CashierLanguageContextHolder.set(paymentInquiryRequest.getLanguage());
        //1.检查交易信息
        OrderPaymentQueryRspDo orderPaymentQueryRspDo = orderService.tradeQry(payRequestNo,tradeToken, paymentInquiryRequest.getVersion());
        orderPaymentQueryRspDo.merchantIdValidate(orderPaymentQueryRspDo.getTradeInfo().getMerchantNo(),paymentInquiryRequest.getMerchantId());
        //2.支付单信息
        ResultPageResponse.OrderDetail orderDetail = buildOrderDetailByOrderPaymentQueryRspDo(orderPaymentQueryRspDo);
        if(StringUtils.isBlank(payRequestNo)){
            return ResultUtil.success(ResultPageResponse.builder().orderDetail(orderDetail).build());
        }
        //3.支付方式信息
        ResultPageResponse.PaymentMethod paymentMethod = buildPayMethod(orderPaymentQueryRspDo, voucherService.getPaymentVoucherIfNullEmpty(voucherService.getPaymentVoucherResponse(payRequestNo)));
        //4.渠道侧信息？是否可以只查询一次
        //4.1渠道侧信息
        List<Map<String, Object>> extendInfos = getExtendInfos(payRequestNo, paymentInquiryRequest.getLanguage());
        ///4.2渠道侧信息
        Map<String, String> extInfoMap = getExtInfoMap(payRequestNo,orderPaymentQueryRspDo);
        //响应
        return ResultUtil.success(getResultPageResponse(orderDetail,paymentMethod,extendInfos,extInfoMap));
    }

    /**
     * paymentInquiry: 支付查询接口2.0
     *
     * @param paymentInquiryRequestV2
     * @return
     */
    @Override
    public Result<ResultPageResponseV2> queryReceivables(PaymentInquiryRequestV2 paymentInquiryRequestV2) {
        //解析JWT
        PaymentInquiryRequestV2 convertByPayLoads = this.convertByPayLoads(paymentInquiryRequestV2);
        if (Objects.nonNull(convertByPayLoads)){
            paymentInquiryRequestV2 = convertByPayLoads;
        }

        String tradeToken = paymentInquiryRequestV2.getTradeToken();
        String cachePayRequestNo = commonCacheService.getAuthPayRequestNoCache(tradeToken, paymentInquiryRequestV2.getCountry());
        String payRequestNo = StringUtils.defaultIfBlank(paymentInquiryRequestV2.getPayRequestNo(), cachePayRequestNo);
        if (StringUtils.isAllBlank(payRequestNo, tradeToken)) {
            log.error("orderService tradeQry param payRequestNo and tradeToken cant be all blank");
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), ErrorCodeEnum.INVALID_PARAMS.getEnName());
        }
        //0.设置前端语言
        CashierLanguageContextHolder.set(paymentInquiryRequestV2.getLanguage());

        //1.检查交易信息（调用订单中心）
        OrderPaymentQueryRspDo orderPaymentQueryRspDo = orderService.tradeQry(payRequestNo, tradeToken, paymentInquiryRequestV2.getVersion());
        orderPaymentQueryRspDo.merchantIdValidate(orderPaymentQueryRspDo.getTradeInfo().getMerchantNo(), paymentInquiryRequestV2.getMerchantId());

        //2. 初始化ResultPageResponseV2，构建商户信息和支付信息
        ResultPageResponseV2 resultPageResponseV2 = initResultPageResponseV2(orderPaymentQueryRspDo);
        if (StringUtils.isBlank(payRequestNo)) {
            return ResultUtil.success(resultPageResponseV2);
        }

        //3.支付方式信息（调用凭证服务，取出支付方式）
        GetPaymentVoucherResponse paymentVoucher = voucherService.getPaymentVoucherResponse(payRequestNo);
        PayStepPaymentScenePaymentInfo paymentVoucherIfNullEmpty = voucherService.getPaymentVoucherIfNullEmpty(paymentVoucher);
        ResultPageResponseV2.VisualInfo visualInfo = buildVisualInfo(orderPaymentQueryRspDo, paymentVoucherIfNullEmpty);

        // 校验结果页是否需要展示绑定按钮
        if (!CommonConstants.SUBSCRIPTION_PRODUCT_CODE.equals(Optional.ofNullable(orderPaymentQueryRspDo.getTradeInfo()).map(OrderPaymentQueryRspDo.TradeInfo::getProductCode).orElse(StrUtil.EMPTY))) {
            paymentManage4WalletAuthAdaptor.doResultPageProcess(resultPageResponseV2, orderPaymentQueryRspDo, paymentVoucherIfNullEmpty, paymentVoucher.getBizIdentity());
        }

        //4.渠道侧信息
        ResultPageResponseV2.OrderInfo.ChannelInfo channelInfo = buildChannelInfo(payRequestNo, paymentInquiryRequestV2.getLanguage(), orderPaymentQueryRspDo);

        //5.埋点
        String eTrack = Optional.ofNullable(paymentVoucherIfNullEmpty).map(PayStepPaymentScenePaymentInfo::getETrack).orElse(StringUtils.EMPTY);

        //6.关联响应结果
        relateResultPageResponseV2(resultPageResponseV2, visualInfo, channelInfo, eTrack);

        return ResultUtil.success(resultPageResponseV2);
    }

    /**
     * 移步存储操作状态
     * @param payResponse
     */
    private void storageOperateStatus(PayRequest payRequest,PayResponse payResponse){
        try{
            if(!storageOperateStatusService.checkPageFromCopy(payRequest.getRiskInfo())){
                return;
            }
            if(!payResponse.isSuccess()){
                return;
            }
            PayResponse.PaymentDataDetail  detail = payResponse.getPaymentDetail();
            if(Objects.isNull(detail)){
                return;
            }
            storageOperateStatusService.asyncStorageOperate(detail.getTradeToken(),PageOperateStatusEnum.PAYING);
        }catch (Exception e){
            log.error("asyncStorageOperateStatus error",e);
        }
    }


    private PaymentInquiryRequestV2 convertByPayLoads(PaymentInquiryRequestV2 paymentInquiryRequestV2) {
        //1.使用固定的payloads对象，约束字段
        PaymentInquiryRequestV2Payloads payloads = JWTUtilExt.parseToken(paymentInquiryRequestV2.getGatewayJWT(), PaymentInquiryRequestV2Payloads.class);
        //2.应该使用 paymentInquiryRequestV2 和 payloads 进行合并内部对象(下次改造时使用该方式)

        //3.本次使用payloads映射到paymentInquiryRequestV2
        return payloadsConvert.toPaymentInquiryRequestV2(payloads);
    }

    /**
     * 初始化：ResultPageResponseV2
     *
     * @param orderPaymentQueryRspDo
     * @return
     */
    private ResultPageResponseV2 initResultPageResponseV2(OrderPaymentQueryRspDo orderPaymentQueryRspDo) {

        ResultPageResponseV2.OrderInfo orderInfo = new ResultPageResponseV2.OrderInfo();
        ResultPageResponseV2.OrderInfo.PaymentInfo paymentInfo = new ResultPageResponseV2.OrderInfo.PaymentInfo();

        String language = StringUtils.defaultString(CashierLanguageContextHolder.get(), CommonConstants.EN);
        String country = StringUtils.EMPTY;
        BigDecimal payAmount = orderPaymentQueryRspDo.getTradeInfo().getTotalAmount();
        String payCurrency = orderPaymentQueryRspDo.getTradeInfo().getCurrency();
        BigDecimal payTotalAmount = orderPaymentQueryRspDo.getTradeInfo().getTotalAmount();

        //支付信息
        paymentInfo.setOrderAmount(payAmount);

        paymentInfo.setOrderCurrency(payCurrency);
        if (null != orderPaymentQueryRspDo.getPaymentDetail()) {
            BigDecimal orderAmount = new BigDecimal(String.valueOf(payAmount));
            paymentInfo.setOrderAmountFormat(moneyHandle.handleMoney(country,String.valueOf(orderAmount),payCurrency));
            payAmount = orderPaymentQueryRspDo.getPaymentDetail().getPayAmount();
            payCurrency = orderPaymentQueryRspDo.getPaymentDetail().getPayCurrency();
            country = orderPaymentQueryRspDo.getPaymentDetail().getCountry();
            //营销：结果页增加优惠金额
            payTotalAmount = orderPaymentQueryRspDo.getPaymentDetail().getPayTotalAmount();
            String finalCountry = country;
            Optional.ofNullable(orderPaymentQueryRspDo.getPaymentDetail()
                            .getDiscount())
                    .ifPresent(item->{
                        paymentInfo.setDiscountAmount(item.getDiscountAmount());
                        paymentInfo.setDiscountAmountFormat(moneyHandle.handleMoney(finalCountry,String.valueOf(item.getDiscountAmount()),item.getDiscountCurrency()));
                    });
        }

        paymentInfo.setTotalAmount(payTotalAmount);
        paymentInfo.setTotalAmountFormat(moneyHandle.handleMoney(country,String.valueOf(payTotalAmount),payCurrency));
        paymentInfo.setPayAmount(payAmount);
        paymentInfo.setPayAmountFormat(moneyHandle.handleMoney(country,String.valueOf(payAmount),payCurrency));
        paymentInfo.setPayCurrency(payCurrency);
        paymentInfo.setFrontCallbackUrl(orderPaymentQueryRspDo.getTradeInfo().getFrontCallbackUrl());
        paymentInfo.setCountry(country);

        //商户信息（汉字转拼音)
        ResultPageResponseV2.MerchantInfo merchantInfo = new ResultPageResponseV2.MerchantInfo();
        MerchantDetailDo merchantDetailDo = this.getMerchantSimpleName(orderPaymentQueryRspDo, country);
        merchantInfo.setMerchantName(pinyinHandle.handle(country, Optional.ofNullable(merchantDetailDo).map(MerchantDetailDo::showMerchantName).orElse(StringUtils.EMPTY)));
        merchantInfo.setMerchantId(orderPaymentQueryRspDo.getTradeInfo().getMerchantNo());

        paymentInfo.setCashierResultStatus(orderService.getResultPageStatusByPayRequestNo(orderPaymentQueryRspDo.getTradeInfo().getMerchantNo(),orderPaymentQueryRspDo.getTradeInfo().getStatus(),
                orderPaymentQueryRspDo.getPayRequestStatus()));
        paymentInfo.setTradeToken(orderPaymentQueryRspDo.getTradeInfo().getTradeToken());
        paymentInfo.setTryAgainUrl(this.getTryAgainUrl(orderPaymentQueryRspDo,merchantDetailDo,language));
        paymentInfo.setShowTryAgain(BooleanUtils.toBoolean(orderPaymentQueryRspDo.getContinuePay()) && StringUtils.isNotBlank(paymentInfo.getTryAgainUrl()));
        paymentInfo.setIsSendReceipt(this.getisSendReceipt(orderPaymentQueryRspDo));
        paymentInfo.setOrderVersion(orderPaymentQueryRspDo.getTradeInfo().getOrderVersion());

        paymentInfo.setIsCashierDomainGrey(orderPaymentQueryRspDo.getTradeInfo().getIsCashierDomainGrey());
        if(StringUtils.isNotBlank(orderPaymentQueryRspDo.getTradeInfo().getErrorMsg())){
            paymentInfo.setFailedCode(orderPaymentQueryRspDo.getTradeInfo().getErrorCode());
            paymentInfo.setFailedMsg(orderPaymentQueryRspDo.getTradeInfo().getErrorMsg());
        }

        //发票地址
        String invoiceUrl = Optional.ofNullable(orderPaymentQueryRspDo)
                .map(OrderPaymentQueryRspDo::getTradeInfo)
                .map(OrderPaymentQueryRspDo.TradeInfo::getTradeFee)
                .map(OrderPaymentQueryRspDo.TradeFee::getMerFee)
                .map(OrderPaymentQueryRspDo.MerFee::getInvoiceUrl)
                .filter(StringUtils::isNotBlank)
                .orElse(StringUtils.EMPTY);
        //只有成功 才会有代缴税表示
        if(OuterTradeOrderStatus.SUCCESS.getCode().equals(paymentInfo.getCashierResultStatus()) && StringUtils.isNotBlank(invoiceUrl)){
            paymentInfo.setInvoiceUrl(invoiceUrl);
            paymentInfo.setTaxDisplayName(behalfTaxInitService.getBehalfTaxInfo(country));
        }
        orderInfo.setPaymentInfo(paymentInfo);

        return ResultPageResponseV2.builder()
                .orderInfo(orderInfo)
                .merchantInfo(merchantInfo)
                .build();
    }

    private String getTryAgainUrl(OrderPaymentQueryRspDo orderPaymentQueryRspDo,MerchantDetailDo merchantDetailDo,String language) {
        if(!orderPaymentQueryRspDo.getContinuePay()){
            return null;
        }
        //TODO ISV-ok,链接支付处理
        //如果是1003链接支付
        ProductDomain productDomain = ProductDomain.builder()
                .productCode(orderPaymentQueryRspDo.getTradeInfo().getProductCode())
                .integrate(orderPaymentQueryRspDo.getTradeInfo().getIntegrate())
                .serviceMode(orderPaymentQueryRspDo.getTradeInfo().getServiceMode())
                .build();
        boolean matchTransCode = ProductCodeDo.create(orderPaymentQueryRspDo.getTradeInfo().getProductCode()).matchProductCode(Arrays.asList(apolloValue.getCashierPayTransCodes().split(CommonConstants.ACCOUNT_SPLIT_STR)));
        matchTransCode = matchTransCode || productDomain.isPaymentLink();
        log.info("getTryAgainUrl tradeToken = {} productDomain = {}", orderPaymentQueryRspDo.getTradeInfo().getTradeToken(),JSON.toJSONString(productDomain));
        if(matchTransCode){
            return commonBusinessService.goHomePageUrl(commonBusinessService.makeAfterPayTradeDetail(Maps.newHashMap(), language, null, orderPaymentQueryRspDo, merchantDetailDo),null);
        }else {
            OrderCashierQueryRspDo orderCashierQueryRspDo = orderService.tradeTokenQry(orderPaymentQueryRspDo.getTradeInfo().getTradeToken(), orderPaymentQueryRspDo.getTradeInfo().getCashierId(), orderPaymentQueryRspDo.getTradeInfo().getMerchantNo());
            boolean supportTryAgain = isSupportTryAgain(orderPaymentQueryRspDo);
            if (Objects.nonNull(orderCashierQueryRspDo) && (orderCashierQueryRspDo.isAssignCard() || supportTryAgain)) {
                return Optional.ofNullable(orderCashierQueryRspDo).map(OrderCashierQueryRspDo::getRedirectUrl).orElse(null);
            }
            return null;

        }
    }

    /**
     * 灰度商户+支付方式支持重试
     * @param orderPaymentQueryRspDo
     * @return
     */
    private boolean isSupportTryAgain(OrderPaymentQueryRspDo orderPaymentQueryRspDo) {
        boolean supportTryAgain = false;
        // 商户号
        String merchantNo = Optional.ofNullable(orderPaymentQueryRspDo.getTradeInfo()).map(OrderPaymentQueryRspDo.TradeInfo::getMerchantNo).orElse(StringUtils.EMPTY);
        // 支付方式
        String paymentMethod = Optional.ofNullable(orderPaymentQueryRspDo.getPaymentDetail()).map(OrderPaymentQueryRspDo.PaymentDetail::getPaymentMethod).orElse(StringUtils.EMPTY);
        if (apolloValue.tryAgainMerchantConfig.containsKey(paymentMethod) || apolloValue.tryAgainMerchantConfig.containsKey("*")) {
            Set<String> merchantNos = Optional.ofNullable(apolloValue.tryAgainMerchantConfig.get(paymentMethod)).orElse(apolloValue.tryAgainMerchantConfig.get("*"));
            if (Objects.nonNull(merchantNos) && (merchantNos.contains(merchantNo) || merchantNos.contains("*"))) {
                supportTryAgain = true;
            }
        }
        return supportTryAgain;
    }

    /**
     * 构建渠道侧信息
     *
     * @param payRequestNo 请求号
     * @param language 语言
     * @param orderPaymentQueryRspDo 订单中心结果
     * @return
     */
    private ResultPageResponseV2.OrderInfo.ChannelInfo buildChannelInfo(String payRequestNo, String language, OrderPaymentQueryRspDo orderPaymentQueryRspDo){

        ResultPageResponseV2.OrderInfo.ChannelInfo channelInfo = new ResultPageResponseV2.OrderInfo.ChannelInfo();

        //渠道侧信息
        channelInfo.setExtInfos(getExtendInfos(payRequestNo, language));
        //渠道侧信息（调用上下文，取出渠道侧支付信息）
        channelInfo.setExtInfoMap(getExtInfoMap(payRequestNo,orderPaymentQueryRspDo));

        return channelInfo;
    }

    /**
     * 构建可视化信息
     * @param orderPaymentQueryRspDo 订单中心信息
     * @param voucherPaymentInfo 凭证信息
     * @return
     */
    private ResultPageResponseV2.VisualInfo buildVisualInfo(OrderPaymentQueryRspDo orderPaymentQueryRspDo, PayStepPaymentScenePaymentInfo voucherPaymentInfo) {
        ResultPageResponseV2.VisualInfo visualInfo = new ResultPageResponseV2.VisualInfo();
        ResultPageResponseV2.VisualInfo.DisplayInfo displayInfo= new ResultPageResponseV2.VisualInfo.DisplayInfo();

        //查询显示信息表
        try {
            if(voucherPaymentInfo==null || orderPaymentQueryRspDo.getPaymentDetail()==null){
                return visualInfo;
            }
            CashierPayOption cashierPayOption = null;
            if(apolloValue.isGetCashierPayOptionByPaymentMethodNewSwitch()){
                cashierPayOption = displayDomainFacilitiesService.getCashierPayOptionByPaymentMethod(voucherPaymentInfo.getPaymentDetail().getPaymentMethod(),
                        voucherPaymentInfo.getPaymentDetail().getTargetOrg(),StrUtilsExt.defaultString(voucherPaymentInfo.getPaymentDetail().getCardOrg(),orderPaymentQueryRspDo.getPaymentDetail().getCardOrg()),orderPaymentQueryRspDo.getPaymentDetail().getBankCode(),
                        orderPaymentQueryRspDo.getPaymentDetail().getPayCurrency(), orderPaymentQueryRspDo.getPaymentDetail().getCountry());
                log.info("执行新功能查询pay option信息，返回option不为空flag={}",Objects.nonNull(cashierPayOption));
            }else {
                cashierPayOption = cashierDisplayManageSupport.getCashierDisplayInfoByPayCode(voucherPaymentInfo.getPaymentDetail().getPaymentMethod(), voucherPaymentInfo.getPaymentDetail().getPaymentMethodType(),
                        voucherPaymentInfo.getPaymentDetail().getTargetOrg(), voucherPaymentInfo.getPaymentDetail().getCardOrg(),
                        orderPaymentQueryRspDo.getPaymentDetail().getPayCurrency(), orderPaymentQueryRspDo.getPaymentDetail().getCountry());
            }
            if (cashierPayOption != null) {
                displayInfo.setDisplayName(cashierPayOption.getDisplayName());
                displayInfo.setDisplayNameKey(cashierPayOption.getDisplayNameKey());
                cashierPayOption = cashierDisplayManageSupport.getCashierDisplayInfoByDisplayCode(cashierPayOption.getParentDisplayCode());
                displayInfo.setDisplayParentName(Optional.ofNullable(cashierPayOption).map(CashierPayOption::getDisplayName).orElse(StringUtils.EMPTY));
                displayInfo.setDisplayParentNameKey(Optional.ofNullable(cashierPayOption).map(CashierPayOption::getDisplayNameKey).orElse(StringUtils.EMPTY));
            }
            //增加埋点信息 不用判空
            displayInfo.setPaymentMode(voucherPaymentInfo.getPaymentDetail().getPaymentMethod());
            displayInfo.setTargetOrg(voucherPaymentInfo.getPaymentDetail().getTargetOrg());
            displayInfo.setAccountNo(memberShipManageSupport.getAccountNo(voucherPaymentInfo.getPaymentParamsMap(), voucherPaymentInfo.getExtension()));
            visualInfo.setDisplayInfo(displayInfo);
        } catch (Exception ee) {
            log.error("get display pay option failed,ee={}", ee.getMessage(),ee);
        }
        return visualInfo;
    }

    /**
     * 将结果页信息组装进response
     *
     * @param resultPageResponseV2 响应结果
     * @param visualInfo 可视化信息
     * @param channelInfo 渠道信息
     * @param eTrack 前端埋点
     */
    private void relateResultPageResponseV2(ResultPageResponseV2 resultPageResponseV2, ResultPageResponseV2.VisualInfo visualInfo, ResultPageResponseV2.OrderInfo.ChannelInfo channelInfo, String eTrack) {
        resultPageResponseV2.setVisualInfo(visualInfo);
        resultPageResponseV2.getOrderInfo().setChannelInfo(channelInfo);
        resultPageResponseV2.setETrack(eTrack);
    }

    private List<Map<String, Object>> getExtendInfos(String payRequestNo,String language) {
        //上下文获取相关信息
        List<Map<String, Object>> list = new ArrayList<>();
        try{
            InquiryPaymentResponse channelPaymentCallback = contextCacheService.getChannelPaymentCallback(payRequestNo, PaymentContextTypeEnum.PAYMENT_CALLBACK);
            return Optional.ofNullable(channelPaymentCallback.getExtendResult()).map(obj->fillExtendInfo(language, obj)).orElse(null);
        }catch (Exception ee){
            log.error("paymentInquiry getExtendInfos failed  " , ee);
        }
        return list;
    }

    private Map<String, String> getExtInfoMap(String payRequestNo,OrderPaymentQueryRspDo orderPaymentQueryRspDo) {
        //支付上下文获取相关信息
        Map<String, String> extInfoMap = Maps.newHashMap();
        try{
            com.payermax.fin.exchange.service.response.PayResponse channelResponse = contextCacheService.getChannelResponse(payRequestNo, PaymentContextTypeEnum.PAYMENT_RESPONSE);
            Optional.ofNullable(channelResponse).map(obj->channelResponseConvert.convert2ChannelResultParams(obj)).ifPresent(val->{
                Optional.ofNullable(val.getReturnParam()).map(param->param.getExtraInfo()).ifPresent(extInfo->{
                    Optional.ofNullable(extInfo.get(CommonConstants.PayResultPage.THIRDORG_ORDERNO)).ifPresent(orderNo->extInfoMap.put(CommonConstants.PayResultPage.TRANSACTION_ID, String.valueOf(orderNo)));
                });
            });
            //TODO 2023.10.26版本后期去掉TRANSACTION_TIME，只保留TRANSACTION_TIMESTAMP
            extInfoMap.put(CommonConstants.PayResultPage.TRANSACTION_TIME, DateUtil.dateFormat(DateUtil.REG_YYMMDD_T_HHMMSS_Z,orderPaymentQueryRspDo.getPayCreateTimeIfNullNow()));
            extInfoMap.put(CommonConstants.PayResultPage.TRANSACTION_TIMESTAMP, String.valueOf(orderPaymentQueryRspDo.getPayCreateTimeIfNullNow().getTime()));
        }catch (Exception ee){
            log.error("paymentInquiry getExtInfoMap failed  " , ee);
        }
        return extInfoMap;
    }

    private List<Map<String, Object>> fillExtendInfo(String language, Map<String, Object> result) {
        //TODO 后期进行多语言化，本逻辑后期优化
        List<Map<String, Object>> extendInfos = new ArrayList<>();
        for (Map.Entry entry : result.entrySet()) {
            Map<String, Object> exMap = new HashMap<>();
            if (MultiLanguageEnum.KNET_REF_NUMBER.getCode().equals(entry.getKey())) {
                String labelName = getLabelName(MultiLanguageEnum.KNET_REF_NUMBER, language);
                exMap.put(CommonConstants.LABLE, labelName);
                exMap.put(CommonConstants.VALUE, entry.getValue());
                extendInfos.add(exMap);
            }

            if (MultiLanguageEnum.THIRD_PARTY_TRANSACTION_NUMBER.getCode().equals(entry.getKey())) {
                String labelName = getLabelName(MultiLanguageEnum.THIRD_PARTY_TRANSACTION_NUMBER, language);
                exMap.put(CommonConstants.LABLE, labelName);
                exMap.put(CommonConstants.VALUE, entry.getValue());
                extendInfos.add(exMap);
            }
        }
        return extendInfos;
    }

    private static String getLabelName(MultiLanguageEnum languageEnum,String language) {
        String labelName = null;
        if (CommonConstants.ZH.equals(language)) {
            labelName = languageEnum.getSimplifiedName();
        }

        if (CommonConstants.ZH_HK.equals(language)||CommonConstants.ZH_TW.equals(language)) {
            labelName = languageEnum.getTraditionalChinese();
        }

        if (!CommonConstants.ZH.equals(language)&&!CommonConstants.ZH_HK.equals(language)&&!CommonConstants.ZH_TW.equals(language)) {
            labelName = languageEnum.getEnName();
        }
        return labelName;
    }


    private ResultPageResponse getResultPageResponse(ResultPageResponse.OrderDetail orderDetail,ResultPageResponse.PaymentMethod paymentMethod,List<Map<String,Object>> extendInfos,Map<String, String> extInfoMap) {
        ResultPageResponse resultPageResponse = new ResultPageResponse();
        resultPageResponse.setPaymentMethod(paymentMethod);
        resultPageResponse.setOrderDetail(orderDetail);
        resultPageResponse.setExtInfos(extendInfos);
        resultPageResponse.setExtInfoMap(extInfoMap);
        return resultPageResponse;
    }

    private ResultPageResponse.PaymentMethod buildPayMethod(OrderPaymentQueryRspDo orderPaymentQueryRspDo,PayStepPaymentScenePaymentInfo voucherPaymentInfo) {
        ResultPageResponse.PaymentMethod paymentMethod = new ResultPageResponse.PaymentMethod();
        //4.查询显示信息表
        try {
            if(voucherPaymentInfo==null || orderPaymentQueryRspDo.getPaymentDetail()==null){
                return paymentMethod;
            }
            CashierPayOption cashierPayOption = null;
            if(apolloValue.isGetCashierPayOptionByPaymentMethodNewSwitch()){
                cashierPayOption = displayDomainFacilitiesService.getCashierPayOptionByPaymentMethod(voucherPaymentInfo.getPaymentDetail().getPaymentMethod(),
                        voucherPaymentInfo.getPaymentDetail().getTargetOrg(), StrUtilsExt.defaultString(voucherPaymentInfo.getPaymentDetail().getCardOrg(),orderPaymentQueryRspDo.getPaymentDetail().getCardOrg()),orderPaymentQueryRspDo.getPaymentDetail().getBankCode(),
                        orderPaymentQueryRspDo.getPaymentDetail().getPayCurrency(), orderPaymentQueryRspDo.getPaymentDetail().getCountry());
                log.info("执行新功能查询pay option信息，返回option不为空flag={}",Objects.nonNull(cashierPayOption));
            }else {
                cashierPayOption = cashierDisplayManageSupport.getCashierDisplayInfoByPayCode(voucherPaymentInfo.getPaymentDetail().getPaymentMethod(), voucherPaymentInfo.getPaymentDetail().getPaymentMethodType(),
                        voucherPaymentInfo.getPaymentDetail().getTargetOrg(), voucherPaymentInfo.getPaymentDetail().getCardOrg(),
                        orderPaymentQueryRspDo.getPaymentDetail().getPayCurrency(), orderPaymentQueryRspDo.getPaymentDetail().getCountry());
            }
            if (cashierPayOption != null) {
                paymentMethod.setDisplayName(cashierPayOption.getDisplayName());
                cashierPayOption = displayDomainFacilitiesService.getCashierDisplayInfoByDisplayCode(cashierPayOption.getParentDisplayCode());
                paymentMethod.setDisplayParentName(Optional.ofNullable(cashierPayOption).map(CashierPayOption::getDisplayName).orElse(StringUtils.EMPTY));
            }
            //增加埋点信息 不用判空
            paymentMethod.setPaymentMode(voucherPaymentInfo.getPaymentDetail().getPaymentMethod());
            paymentMethod.setTargetOrg(voucherPaymentInfo.getPaymentDetail().getTargetOrg());
            paymentMethod.setETrack(voucherPaymentInfo.getETrack());
            paymentMethod.setAccountNo(memberShipManageSupport.getAccountNo(voucherPaymentInfo.getPaymentParamsMap(), voucherPaymentInfo.getExtension()));
        } catch (Exception ee) {
            log.error("get display pay option failed,ee={}", ee.getMessage(),ee);
        }
        return paymentMethod;
    }

    private ResultPageResponse.OrderDetail buildOrderDetailByOrderPaymentQueryRspDo(OrderPaymentQueryRspDo orderPaymentQueryRspDo) {
        String language = StringUtils.defaultString(CashierLanguageContextHolder.get(), CommonConstants.EN);

        ResultPageResponse.OrderDetail orderDetail = new ResultPageResponse.OrderDetail();
        String country = StringUtils.EMPTY;
        BigDecimal payAmount = orderPaymentQueryRspDo.getTradeInfo().getTotalAmount();
        String payCurrency = orderPaymentQueryRspDo.getTradeInfo().getCurrency();
        BigDecimal payTotalAmount = orderPaymentQueryRspDo.getTradeInfo().getTotalAmount();
        orderDetail.setOrderAmount(payAmount);
        orderDetail.setOrderCurrency(payCurrency);
        if (null != orderPaymentQueryRspDo.getPaymentDetail()) {
            payAmount = orderPaymentQueryRspDo.getPaymentDetail().getPayAmount();
            payCurrency = orderPaymentQueryRspDo.getPaymentDetail().getPayCurrency();
            country = orderPaymentQueryRspDo.getPaymentDetail().getCountry();
            //营销：结果页增加优惠金额
            payTotalAmount = orderPaymentQueryRspDo.getPaymentDetail().getPayTotalAmount();
            Optional.ofNullable(orderPaymentQueryRspDo.getPaymentDetail()
                            .getDiscount())
                    .ifPresent(item->{
                        orderDetail.setDiscountAmount(item.getDiscountAmount());
                    });
        }
        orderDetail.setTotalAmount(payTotalAmount);
        orderDetail.setPayAmount(payAmount);
        orderDetail.setPayCurrency(payCurrency);
        orderDetail.setFrontCallbackUrl(orderPaymentQueryRspDo.getTradeInfo().getFrontCallbackUrl());
        orderDetail.setMerchantId(orderPaymentQueryRspDo.getTradeInfo().getMerchantNo());
        orderDetail.setCountry(country);
        //汉字转拼音
        MerchantDetailDo merchantDetailDo = this.getMerchantSimpleName(orderPaymentQueryRspDo, country);
        orderDetail.setMerchantName(pinyinHandle.handle(country, Optional.ofNullable(merchantDetailDo).map(MerchantDetailDo::showMerchantName).orElse(StringUtils.EMPTY)));
        orderDetail.setCashierResultStatus(orderService.getResultPageStatusByPayRequestNo(orderPaymentQueryRspDo.getTradeInfo().getMerchantNo(),orderPaymentQueryRspDo.getTradeInfo().getStatus(),
                orderPaymentQueryRspDo.getPayRequestStatus()));
        orderDetail.setTradeToken(orderPaymentQueryRspDo.getTradeInfo().getTradeToken());
        orderDetail.setTryAgainUrl(this.getTryAgainUrl(orderPaymentQueryRspDo,merchantDetailDo,language));
        orderDetail.setShowTryAgain(BooleanUtils.toBoolean(orderPaymentQueryRspDo.getContinuePay()) && StringUtils.isNotBlank(orderDetail.getTryAgainUrl()));
        orderDetail.setIsSendReceipt(this.getisSendReceipt(orderPaymentQueryRspDo));
        orderDetail.setOrderVersion(orderPaymentQueryRspDo.getTradeInfo().getOrderVersion());

        orderDetail.setIsCashierDomainGrey(orderPaymentQueryRspDo.getTradeInfo().getIsCashierDomainGrey());
        if(StringUtils.isNotBlank(orderPaymentQueryRspDo.getTradeInfo().getErrorMsg())){
            orderDetail.setFailedMsg(orderPaymentQueryRspDo.getTradeInfo().getErrorMsg());
        }
        //只有成功 才会有代缴税表示
        if(!OuterTradeOrderStatus.SUCCESS.getCode().equals(orderDetail.getCashierResultStatus())
         || orderPaymentQueryRspDo.getTradeInfo().getTradeFee() == null
                || orderPaymentQueryRspDo.getTradeInfo().getTradeFee().getMerFee() == null
        || StringUtils.isEmpty(orderPaymentQueryRspDo.getTradeInfo().getTradeFee().getMerFee().getInvoiceUrl())){
            return orderDetail;
        }
        orderDetail.setInvoiceUrl(orderPaymentQueryRspDo.getTradeInfo().getTradeFee().getMerFee().getInvoiceUrl());
        orderDetail.setTaxDisplayName(behalfTaxInitService.getBehalfTaxInfo(country));
        return orderDetail;
    }


    private MerchantDetailDo getMerchantSimpleName(OrderPaymentQueryRspDo orderPaymentQueryRspDo, String country) {
        try{
            MerchantDetailDo merchantDetailDo = merchantMemberManage.queryMemberMerchant(commonBusinessManageSupport.createMerchantBaseQry(orderPaymentQueryRspDo.getTradeInfo(),country));
            return merchantDetailDo;
        }catch (Exception ee){
            log.error("payment result page getMerchantSimpleName,country={},orderPaymentQueryRspDo={}",country,orderPaymentQueryRspDo,ee);
        }
        return null;
    }


    private Boolean getisSendReceipt(OrderPaymentQueryRspDo orderPaymentQueryRspDo) {

        return false;
    }

    @Override
    public String processingPay(ProcessPayRequest processPayRequest) {
        return commonBusinessService.putUrlIntoHtml(getPayResponseUrl(processPayRequest).getRequestUrl());
    }

    private PayResponse getPayResponseUrl(ProcessPayRequest processPayRequest) {
        String payResponseUrl = null;
        PaymentInstanceDo paymentInstance =null;
        //1.查询交易信息
        OrderCashierQueryRspDo orderCashierQueryRspDo= orderService.tradeTokenQry(processPayRequest.getTradeToken(), processPayRequest.getCashierId(),processPayRequest.getMerchantId());
        BusinessException.fastException(orderCashierQueryRspDo.getMerchantNo().equals(processPayRequest.getMerchantId()),ErrorCodeEnum.ILLEGAL_OPERATION.getCode(), ErrorCodeEnum.ILLEGAL_OPERATION.getEnName());

        try{
            ProcessingPayApply processingPayApply = this.getProcessingPayApply(processPayRequest,orderCashierQueryRspDo);
            paymentInstance = processingPayApply.getPaymentInstance();
            //3.支付
            PaymentResult paymentResult =payManageSupportMap.get(paymentInstance.getNextApiType()).processingPayExecute(processingPayApply.getOrderPayRequest(),true,orderCashierQueryRspDo,paymentInstance,processingPayApply.getCashierCoreDetailDo());
            PayResponse payResponse = CommonBusinessManageSupport.createSuccessOrFailedPayResponse(paymentResult);
            return payResponse;
        }catch (BusinessException4ThrowOutData be){
            if(ErrorCodeEnum.DISCOUNT_NOT_MATCH.getCode().equals(be.getErrCode())){
                return PayResponse.builder().success(false).errorCode(ErrorCodeEnum.DISCOUNT_NOT_MATCH.getCode()).errorMsg(ErrorCodeEnum.DISCOUNT_NOT_MATCH.getDesc()).build();
            }
            if(StringUtils.equalsAnyIgnoreCase(be.getErrCode(),
                    ErrorCodeEnum.CONTINUE_TO_PAY_BY_EXISTS_URL.getCode(),ErrorCodeEnum.CONTINUE_TO_PAY_BY_EXISTS_URL.getCode())
                  ||  mappingErrorCodeService.isContainsGoResultPageErrorCode(be.getErrCode()) ){
                PaymentResult paymentResult =  (PaymentResult) be.getData();
                return PayResponse.buildOnlyUrlPayResponse(paymentResult.getRequestUrl(), ResultTypeEnum.DIRECT_LINK.getCode());
            }
            OutErrorData outErrorData = bizCodeMessageService.queryOutMsgByErrCode(be.getErrCode(),processPayRequest.getLanguage(),be.getMessage());
            payResponseUrl = commonBusinessService.getErrorHtml(outErrorData,orderCashierQueryRspDo,processPayRequest.getVersion(),processPayRequest.getCountry(),processPayRequest.getLanguage());
        }catch (BusinessException be){
            log.info("processingPay go BusinessException logic, tradeToken is={}, BusinessException={}",processPayRequest.getTradeToken(), be);
            if(ErrorCodeEnum.DISCOUNT_NOT_MATCH.getCode().equals(be.getErrCode())){
                return PayResponse.builder().success(false).errorCode(ErrorCodeEnum.DISCOUNT_NOT_MATCH.getCode()).errorMsg(ErrorCodeEnum.DISCOUNT_NOT_MATCH.getDesc()).build();
            }
            if (Objects.nonNull(AccountErrorCodeTypeEnum.of(be.getErrCode()))) {
                ProcessingPayCache processingPayCache = getProcessingPayCache(be,orderCashierQueryRspDo.getPayParams(),paymentInstance);
                //将错误信息，processing页支付标识放入缓存
                commonCacheService.putProcessingPayCache(processPayRequest.getTradeToken(), processPayRequest.getCountry(), processingPayCache);
                String homePageUrl = commonBusinessService.goHomePageUrl(tradeDetailConvert.convertToTradeDetail(processPayRequest, orderCashierQueryRspDo), paymentInstance);
                return PayResponse.buildOnlyUrlPayResponse(homePageUrl, ResultTypeEnum.DIRECT_LINK.getCode());
            }
            OutErrorData outErrorData = bizCodeMessageService.queryOutMsgByErrCode(be.getErrCode(),processPayRequest.getLanguage(),be.getMessage());
            payResponseUrl = commonBusinessService.getErrorHtml(outErrorData,orderCashierQueryRspDo,processPayRequest.getVersion(),processPayRequest.getCountry(),processPayRequest.getLanguage());
        }catch (Exception ee){
            log.error("processingPay go Exception logic,msg: {}",ee.getMessage(),ee);
            payResponseUrl = commonBusinessService.getErrorHtml(OutErrorData.defaultOutErrorData(),orderCashierQueryRspDo,processPayRequest.getVersion(),processPayRequest.getCountry(),processPayRequest.getLanguage());
        }
        return PayResponse.buildOnlyUrlPayResponse(payResponseUrl, ResultTypeEnum.DIRECT_LINK.getCode());
    }

    public ProcessingPayApply getProcessingPayApply(ProcessPayRequest processPayRequest, OrderCashierQueryRspDo orderCashierQueryRspDo) {
        CashierRequest cashierRequest = CommonBusinessManageSupport.createCashierRequest(processPayRequest, orderCashierQueryRspDo);
        cashierRequest.setPmmaxToken(processPayRequest.getPmmaxToken());
        //1.2从指定的订单中取出实例信息
        updateProcessPayRequestAndCheck(processPayRequest, orderCashierQueryRspDo);

        //2.获取支付实例信息
        CashierCoreDetailDo cashierCoreDetailDo = cashierCorePaymentManage.getCashierCoreDetaiV2(cashierRequest, processPayRequest.getCountry(), false);
        BusinessException.fastException(CollectionUtils.isNotEmpty(cashierCoreDetailDo.getInstanceList()),ErrorCodeEnum.NO_AVAILABLE_INSTANCE.getCode(),ErrorCodeEnum.NO_AVAILABLE_INSTANCE.getEnName());

        OrderPayRequest orderPayRequest = orderPayRequestConvert.convertFromProcessPayRequest(processPayRequest,orderCashierQueryRspDo.getPciCardDomainDo());

        //判断是否是PMMXToken支付
        CardAuthRspDo cardAuthRspDo = StringUtils.isNotBlank(orderPayRequest.getPmmaxToken()) ?
                authManageSupport.queryCardAuthByPmmaxToken(orderCashierQueryRspDo, orderPayRequest.getPmmaxToken()) : null;

        //从实例中过滤可用的实例
        PaymentInstanceDo paymentInstance = (PaymentInstanceDo) BranchHelper.matchSupTureOrFalse(StringUtils.isNotBlank(orderPayRequest.getPmmaxToken())).trueOrFalseHandle(
                () -> getTokenPaymentInstanceDo(orderCashierQueryRspDo, cashierCoreDetailDo, orderPayRequest, cardAuthRspDo),
                () -> knowPaymentInstanceService.knowPaymentInstanceByEqualsTo3ElementsExtend(cashierCoreDetailDo.getInstanceList(), orderPayRequest.getPaymentMode(), orderPayRequest.getTargetOrg(),orderCashierQueryRspDo.getCardOrg())
        );

        updatePayRequest(orderPayRequest, paymentInstance);

        return ProcessingPayApply.builder()
                .orderCashierQueryRspDo(orderCashierQueryRspDo)
                .cashierCoreDetailDo(cashierCoreDetailDo)
                .paymentInstance(paymentInstance)
                .orderPayRequest(orderPayRequest)
                .cardAuthRspDo(cardAuthRspDo)
                .build();
    }

    private PaymentInstanceDo getTokenPaymentInstanceDo(OrderCashierQueryRspDo orderCashierQueryRspDo, CashierCoreDetailDo cashierCoreDetailDo,
                                                           OrderPayRequest orderPayRequest,CardAuthRspDo cardAuthRspDo) {
        if (orderCashierQueryRspDo.getInteractive() == 2) {
            log.info("getTokenPaymentInstanceDo is api tradeToken = {}", orderCashierQueryRspDo.getTradeToken());
            return getTokenPaymentInstanceDoNew(orderCashierQueryRspDo,cashierCoreDetailDo,orderPayRequest,cardAuthRspDo);
        }
        if ("old".equalsIgnoreCase(apolloValue.getCashierProcessUseVersion())) {
            return getTokenPaymentInstanceDoOld(orderCashierQueryRspDo,cashierCoreDetailDo,orderPayRequest,cardAuthRspDo);
        } else if ("compare".equalsIgnoreCase(apolloValue.getCashierProcessUseVersion())) {
            PaymentInstanceDo newDo = null;
            PaymentInstanceDo old = null;
            try {
                newDo = getTokenPaymentInstanceDoNew(orderCashierQueryRspDo,cashierCoreDetailDo,orderPayRequest,cardAuthRspDo);
            } catch (Exception e) {
                log.info("getTokenPaymentInstanceDo compare newDo with exception", e);
            }
            old = getTokenPaymentInstanceDoNew(orderCashierQueryRspDo,cashierCoreDetailDo,orderPayRequest,cardAuthRspDo);
            return compareInstance(newDo,old,orderCashierQueryRspDo.getTradeToken());
        } else {
            return getTokenPaymentInstanceDoNew(orderCashierQueryRspDo,cashierCoreDetailDo,orderPayRequest,cardAuthRspDo);
        }
    }

    public PaymentInstanceDo compareInstance(PaymentInstanceDo newInstance, PaymentInstanceDo old, String tradeToken) {
        try {
            if (Objects.isNull(newInstance)){
                log.info("getTokenPaymentInstanceDo newInstance is null tradeToken = {}",tradeToken);
            }
            if (Objects.isNull(old)){
                log.info("getTokenPaymentInstanceDo old is null tradeToken = {}",tradeToken);
            }
            if (!old.getCardOrg().equalsIgnoreCase(newInstance.getCardOrg())) {
                log.info("getTokenPaymentInstanceDo compare card org not the same, old ={}, newDo {}",JSON.toJSONString(old), JSON.toJSONString(newInstance));
            }
            if (!old.getPayAmount().getCurrency().equalsIgnoreCase(newInstance.getPayAmount().getCurrency())) {
                log.info("getTokenPaymentInstanceDo compare currency not the same, old ={}, newDo {}",JSON.toJSONString(old), JSON.toJSONString(newInstance));
            }
        } catch (Exception e) {
            log.info("compareInstance with error tradeToken = {}",tradeToken,e);
        }
        return old;
    }

    private PaymentInstanceDo getTokenPaymentInstanceDoOld(OrderCashierQueryRspDo orderCashierQueryRspDo, CashierCoreDetailDo cashierCoreDetailDo,
                                                        OrderPayRequest orderPayRequest,CardAuthRspDo cardAuthRspDo) {
        BusinessException.fastException(cardAuthRspDo.isPmmaxTokenIsAvaliable(),ErrorCodeEnum.PAYMENT_TOKEN_INVALID.getCode(),"auth info is empty");

        CardAuthRspDo.CardAuthItem cardAuthItem = cardAuthRspDo.getCardAuthItemList().get(0);//NO_CHECK 授权服务保证
        List<PaymentInstanceDo> paymentInstanceDos = knowPaymentInstanceService.knowPaymentInstanceByPaymentMode(cashierCoreDetailDo, orderPayRequest.getPaymentMode(), orderPayRequest.getTargetOrg());
        List<CardBinInfo> cardBinInfos = Optional.ofNullable(cardAuthItem).map(CardAuthRspDo.CardAuthItem::getCardAuthItemExtend).map(CardAuthRspDo.CardAuthItemExtend::getCardBinInfoList).orElse(Lists.newArrayList());
        List<PaymentInstanceDo> filterPaymentInstanceDos = Lists.newArrayList();
        paymentInstanceDos.stream().forEach(obj-> cardBinInfos.stream().forEach(val->{
            if (val.getCardOrganization().equals(obj.getCardOrg())) {
                filterPaymentInstanceDos.add(obj);
            }
        }));
        BusinessException.fastException(CollectionUtils.isNotEmpty(filterPaymentInstanceDos),ErrorCodeEnum.NO_AVAILABLE_INSTANCE.getCode(), ErrorCodeEnum.NO_AVAILABLE_INSTANCE.getEnName());
        orderPayRequest.setCardAuthRspDo(cardAuthRspDo);
        List<PaymentInstanceDo> availableInstances = filterPaymentInstanceDos.stream().filter(obj -> obj.getCardOrg().equals(cardAuthItem.getCardOrg())).collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(availableInstances) ? availableInstances.stream().findFirst().get() : filterPaymentInstanceDos.stream().findAny().get();
    }


    private PaymentInstanceDo getTokenPaymentInstanceDoNew(OrderCashierQueryRspDo orderCashierQueryRspDo, CashierCoreDetailDo cashierCoreDetailDo,
                                                        OrderPayRequest orderPayRequest,CardAuthRspDo cardAuthRspDo) {
        BusinessException.fastException(cardAuthRspDo.isPmmaxTokenIsAvaliable(),ErrorCodeEnum.PAYMENT_TOKEN_INVALID.getCode(),"auth info is empty");

        CardAuthRspDo.CardAuthItem cardAuthItem = cardAuthRspDo.getCardAuthItemList().get(0);//CHECKED 查询处已经见过过size
        if (Objects.isNull(cardAuthItem) || Objects.isNull(cardAuthItem.getCardIdentify())) {
            log.info("tradeToken {} have invalid cardAuthItem {}", orderCashierQueryRspDo.getTradeToken(), JSON.toJSONString(cardAuthRspDo.getCardAuthItemList()));
            throw new BusinessException(ErrorCodeEnum.PAYMENT_TOKEN_INVALID.getCode(), ErrorCodeEnum.PAYMENT_TOKEN_INVALID.getEnName());
        }
        List<PaymentInstanceDo> paymentInstanceDos = knowPaymentInstanceService.knowPaymentInstanceByPaymentMode(cashierCoreDetailDo, orderPayRequest.getPaymentMode(), orderPayRequest.getTargetOrg());
//        List<CardBinInfo> cardBinInfos = Optional.ofNullable(cardAuthItem).map(CardAuthRspDo.CardAuthItem::getCardAuthItemExtend).map(CardAuthRspDo.CardAuthItemExtend::getCardBinInfoList).orElse(Lists.newArrayList());
        //1.修该为使用授权服务卡ID查询卡宾
        String cardIdentifierNo = cardAuthItem.getCardIdentify();
        String country = orderPayRequest.getCountry();
        CardBinDTO cardBinDTO = cardRemoteService.getCardBinInfo(cardIdentifierNo, country);
        List<CardBinCountryDTO> cardBins = cardBinFacilitiesService.getCardBinWithDefault(country, cardBinDTO.getCardBinNo());
        // 卡宾为空,则认为token为invalid
        if (CollectionUtils.isEmpty(cardBins)) {
            throw new BusinessException(ErrorCodeEnum.PAYMENT_TOKEN_INVALID.getCode(), ErrorCodeEnum.PAYMENT_TOKEN_INVALID.getEnName());
        }
        //2.使用卡前缀再次查询卡宾服务获取卡宾信息
        PaymentInstanceDo selected = paymentInstanceDos.stream()
                .filter(l->(Objects.nonNull(l.getCardOrg()) && l.getCardOrg().equalsIgnoreCase(cardBins.get(0).getCardOrganization())))//NO_CHECK
                .filter(l->l.getPayAmount().getCurrency().equalsIgnoreCase(orderCashierQueryRspDo.getOrderCurrency()))
                .findAny().orElse(null);
        if (cardBins.size() > 1 && Objects.isNull(selected)) {
            selected = paymentInstanceDos.stream()
                    .filter(l->(Objects.nonNull(l.getCardOrg()) && l.getCardOrg().equalsIgnoreCase(cardBins.get(1).getCardOrganization())))
                    .filter(l->l.getPayAmount().getCurrency().equalsIgnoreCase(orderCashierQueryRspDo.getOrderCurrency()))
                    .findAny().orElse(null);
        }
        BusinessException.fastException(Objects.nonNull(selected),ErrorCodeEnum.NO_AVAILABLE_INSTANCE.getCode(), ErrorCodeEnum.NO_AVAILABLE_INSTANCE.getEnName());
        orderPayRequest.setCardAuthRspDo(cardAuthRspDo);
        return selected;
    }

    @Override
    public Result<PayResponse> processingPayV2(ProcessPayRequest processPayRequest) {
        PayResponse payResponse = this.getPayResponseUrl(processPayRequest);
        return payResponse.isSuccess() ? ResultUtil.success(payResponse) : ResultUtil.fail(payResponse.getErrorCode(), payResponse.getErrorMsg());
    }

    @Override
    public Result<PayResponse> processingPayV3(ProcessPayRequest processPayRequest) {
        //1.查询交易信息
        OrderCashierQueryRspDo orderCashierQueryRspDo= orderService.tradeTokenQry(processPayRequest.getTradeToken(), processPayRequest.getCashierId(),processPayRequest.getMerchantId());
        BusinessException.fastException(orderCashierQueryRspDo.getMerchantNo().equals(processPayRequest.getMerchantId()),ErrorCodeEnum.ILLEGAL_OPERATION.getCode(), ErrorCodeEnum.ILLEGAL_OPERATION.getEnName());
        //2.获取
        ProcessingPayApply processingPayApply = this.getProcessingPayApply(processPayRequest, orderCashierQueryRspDo);
        //3.支付路由和请求
        PaymentResult paymentResult =payManageSupportMap.get(processingPayApply.getPaymentInstance().getNextApiType()).processingPayExecute(processingPayApply.getOrderPayRequest(),true,orderCashierQueryRspDo,processingPayApply.getPaymentInstance(),processingPayApply.getCashierCoreDetailDo());
        //4.响应转换
        PayResponse payResponse = CommonBusinessManageSupport.createSuccessOrFailedPayResponse(paymentResult);
        return payResponse.isSuccess() ? ResultUtil.success(payResponse) : ResultUtil.fail(payResponse.getErrorCode(), payResponse.getErrorMsg(), payResponse);
    }

    @Override
    public Result<PayResponse> processApi(ProcessApiRequest request) {
        //1.查询交易信息
        OrderCashierQueryRspDo orderCashierQueryRspDo= orderService.tradeTokenQry(request.getTradeToken(), request.getCashierId(), request.getMerchantId());
        orderCashierQueryRspDo.merchantIdValidate(request.getMerchantId(),orderCashierQueryRspDo.getMerchantNo());
        ProcessingPayApply processingPayApply = null;
        try {
            //设置cardOrg
            orderCashierQueryRspDo.setPaymentMode(StrUtilsExt.defaultString(request.getPaymentMode(),orderCashierQueryRspDo.getPaymentMode()));
            orderCashierQueryRspDo.setCardOrg(StrUtilsExt.defaultString(request.getCardOrg(),orderCashierQueryRspDo.getCardOrg()));
            //2.获取
            processingPayApply = this.getProcessingPayApply(request, orderCashierQueryRspDo);
            //3.支付路由和请求
            PaymentResult paymentResult =payManageSupportMap.get(processingPayApply.getPaymentInstance().getNextApiType()).processingPayExecute(processingPayApply.getOrderPayRequest(),true,orderCashierQueryRspDo,processingPayApply.getPaymentInstance(),processingPayApply.getCashierCoreDetailDo());
            //4.响应转换
            PayResponse payResponse = CommonBusinessManageSupport.createSuccessOrFailedPayResponse(paymentResult);
            if(payResponse!=null && StringUtils.isNotBlank(payResponse.getRequestUrl())){
                return ResultUtil.success(payResponse);
            }
            log.error("tradeToken {} processApi response no redirect url but need, now only back to merchant",request.getTradeToken());
            AssertUtil.isTrue(false, ErrorCodeEnum.SYSTEM_ERROR.getCode(),"processApi response no redirect url");
        }catch (Exception ee){
            log.info("tradeToken {} processApi pay failed, fail msg = {}",request.getTradeToken(),ee.getMessage());
        }
        OrderCashierQueryRspDo cashierQueryRspDo = orderService.tradeTokenQry(request.getTradeToken(), request.getCashierId(), request.getMerchantId());
        String resultPageUrl = commonPageService.getResultPageUrlByPayRequestNo(CashierPayRequestContextHolder.getPayRequestNo(), request.getLanguage(),cashierQueryRspDo, Objects.isNull(processingPayApply)?null:processingPayApply.getCashierCoreDetailDo().getMerchantDetailDo());
        return ResultUtil.success(PayResponse.buildOnlyUrlPayResponse(resultPageUrl, ResultTypeEnum.DIRECT_LINK.getCode()));
    }

    public Result<PayResponse> cashierProcessingPay(OrderPayRequest orderPayRequest){
        PaymentResult paymentResult = payManageSupportMap.get(orderPayRequest.getNextApiType()).orderPayExecute(orderPayRequest,true);
        PayResponse payResponse = CommonBusinessManageSupport.createSuccessOrFailedPayResponse(paymentResult);
        return ResultUtil.success(payResponse);
    }

    private ProcessingPayCache getProcessingPayCache(BusinessException be,Map<String, String> payParams,PaymentInstanceDo paymentInstance) {
        ProcessingPayCache processingPayCache = accountHandleMappings.get(be.getErrCode()).apply(be);
        processingPayCache.setIsProcessingPay(true);
        processingPayCache.setPayAccountNoMap(getPayAccountNoMap(payParams, paymentInstance));
        return processingPayCache;
    }

    private Map<String, String> getPayAccountNoMap(Map<String, String> payParams, PaymentInstanceDo paymentInstance) {
        Map<String, String> payAccountNoMap = Maps.newHashMap();
        String columnMapping = StringUtils.defaultIfBlank(paymentInstance.getColumnMapping(), StringUtils.EMPTY);
        String[] accountSplit = columnMapping.split(CommonConstants.ACCOUNT_SPLIT_STR);
        Arrays.stream(accountSplit).forEach(val -> payAccountNoMap.put(val, payParams.get(val)));
        return payAccountNoMap;
    }

    private void updateProcessPayRequestAndCheck(ProcessPayRequest processPayRequest, OrderCashierQueryRspDo orderCashierQueryRspDo) {
        processPayRequest.setCountry(orderCashierQueryRspDo.getCountry());
        processPayRequest.setPaymentMode(orderCashierQueryRspDo.getPaymentMode());
        processPayRequest.setTargetOrg(orderCashierQueryRspDo.getTargetOrg());
        processPayRequest.setPayCurrency(orderCashierQueryRspDo.getOrderCurrency());
        //检查cashierRequestNo
        String cashierRequestNo = processPayRequest.getCashierRequestNo();
        if(StringUtils.isBlank(cashierRequestNo)){
            log.error("前端未上送cashierRequestNo，或用户手动修改，日志告警提醒,request={}",processPayRequest);
        }
        cashierRequestNo = cashierRequestNoCheckService.checkProcessingPayCashierRequestNo(payRequestConvert.convertToCashierRequestNoBO(processPayRequest, businessRuleService));
        processPayRequest.setCashierRequestNo(cashierRequestNo);

    }

    private void updatePayRequest(OrderPayRequest orderPayRequest, PaymentInstanceDo paymentInstance) {
        orderPayRequest.setPaymentMethodType(paymentInstance.getPaymentMethodType());
        orderPayRequest.setTargetOrg(paymentInstance.getTargetOrg());
        orderPayRequest.setPaymentFlow(paymentInstance.getPaymentFlow());
        orderPayRequest.setNextApiType(paymentInstance.getNextApiType());
    }

    @Override
    public Result<PayResponse> paymentCallback(PaymentCallbackRequest paymentCallbackRequest) {
        if(!isUatCallback){
            log.error("cant callback,prod env not allow");
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR.getCode(),ErrorCodeEnum.SYSTEM_ERROR.getEnName());
        }
        AssertUtil.isTrue(!StringUtils.isAllBlank(paymentCallbackRequest.getPayRequestNo(),paymentCallbackRequest.getTradeToken()),ErrorCodeEnum.INVALID_PARAMS.getCode(),ErrorCodeEnum.INVALID_PARAMS.getEnName());

        //获取callback执行类
        UatPaymentCallbackManage uatPaymentCallbackManage = uatPaymentCallbackManageMap.get(paymentCallbackRequest.getNotifyType());
        PaymentResult paymentResult = uatPaymentCallbackManage.uatPaymentCallback(paymentCallbackRequest);
        //响应
        PayResponse payResponse = new PayResponse();
        payResponse.setResultType(paymentResult.getResultType());
        payResponse.setRequestUrl(paymentResult.getRequestUrl());

        return ResultUtil.success(payResponse);
    }

    private ProcessingPayCache doAccountAction(BusinessException be) {
        ProcessingPayCache.AccountErrors accountErrors = new ProcessingPayCache.AccountErrors();
        accountErrors.setErrorCode(ErrorCodeEnum.ACCOUNT_INVALID.getCode());
        accountErrors.setErrorMsg(be.getMessage());
        ProcessingPayCache.AccountErrorInfo accountErrorInfo = new ProcessingPayCache.AccountErrorInfo();
        accountErrorInfo.setAccountNo(accountErrors);
        return ProcessingPayCache.builder().accountErrorInfo(accountErrorInfo).build();
    }

    private ProcessingPayCache doEmailAction(BusinessException be) {
        ProcessingPayCache.AccountErrors accountErrors = new ProcessingPayCache.AccountErrors();
        accountErrors.setErrorCode(ErrorCodeEnum.EMAIL_INVALID.getCode());
        accountErrors.setErrorMsg(be.getMessage());
        ProcessingPayCache.AccountErrorInfo accountErrorInfo = new ProcessingPayCache.AccountErrorInfo();
        accountErrorInfo.setBuyerEmail(accountErrors);
        return ProcessingPayCache.builder().accountErrorInfo(accountErrorInfo).build();
    }

    private ProcessingPayCache doPhoneAction(BusinessException be) {
        ProcessingPayCache.AccountErrors accountErrors = new ProcessingPayCache.AccountErrors();
        accountErrors.setErrorCode(be.getErrCode());
        accountErrors.setErrorMsg(be.getMessage());
        ProcessingPayCache.AccountErrorInfo accountErrorInfo = new ProcessingPayCache.AccountErrorInfo();
        accountErrorInfo.setBuyerPhoneNo(accountErrors);
        return ProcessingPayCache.builder().accountErrorInfo(accountErrorInfo).build();
    }
}
