package com.ushareit.cashier.reform.preservative.inner.transformer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.payermax.basic.contexcenter.service.client.enums.PaymentContextTypeEnum;
import com.payermax.basic.contexcenter.service.client.model.payment.PaymentContextInfo;
import com.payermax.basic.contexcenter.service.client.model.payment.PaymentContextSaveRequestDTO;
import com.payermax.basic.contexcenter.service.client.model.payment.PaymentRequestContext;
import com.payermax.cashier.core.facade.model.base.PaymentInstance;
import com.payermax.common.lang.util.IpUtils;
import com.payermax.fin.exchange.service.context.PayRequestContext;
import com.payermax.fin.exchange.service.enums.RequestTypeEnum;
import com.payermax.merchant.facade.resp.ContractSignResp;
import com.payermax.merchant.facade.resp.item.member.SubMerchantBaseInfo;
import com.payermax.order.dto.request.TradeOrderRequest;
import com.ushareit.cashier.client.dto.SignMethodInfoDTO;
import com.ushareit.cashier.constants.CommonConstants;
import com.ushareit.cashier.covert.SubMerchantConvert;
import com.ushareit.cashier.enums.ExtendPropertyEnum;
import com.ushareit.cashier.reform.common.PaymentCommon;
import com.ushareit.cashier.reform.domain.PaymentDomainContext;
import com.ushareit.cashier.reform.facilities.nacos.NacosConfig;
import com.ushareit.cashier.reform.facilities.remote.RiskContextRemoteService;
import com.ushareit.cashier.reform.preservative.inner.convert.GoodsDetailPreservativeConvert;
import com.ushareit.cashier.reform.preservative.inner.convert.PaymentInstancePreservativeConvert;
import com.ushareit.cashier.reform.utils.PaymentUtils;
import com.ushareit.cashier.transformer.PayRequestContextTransformer;
import com.ushareit.cashier.utils.StrUtilsExt;
import com.ushareit.fintech.components.enums.cashier.PaymentIntegrationEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ushareit.cashier.service.BasicContextCacheService.AUTH_CAPTURE_MANUAL;

@Slf4j
@Service
public class PaymentContextPreservativeTransformer {
    @Autowired
    private PaymentInstancePreservativeConvert convert;
    @Autowired
    private GoodsDetailPreservativeConvert goodsDetailPreservativeConvert;
    @Autowired
    private SubMerchantConvert subMerchantConvert;
    @Autowired
    private NacosConfig nacosConfig;
    @Autowired
    private PayRequestContextTransformer transformer;

    public PaymentContextInfo toPaymentContextInfo(PaymentDomainContext context, TradeOrderRequest orderRequest) {
        // TODO 之前逻辑中有移除，AUTH_ACCOUNT_NO CVV_LENGTH逻辑
        PayRequestContext request = new PayRequestContext();
        request.setShippingInfo(buildShipping(orderRequest));
        request.setBillingInfo(buildBilling(orderRequest));
        request.setMerchantInfo(buildMerchantInfo(context));
        request.setOutUserInfo(buildOutUserInfo(orderRequest));
        request.setGoodsDetails(buildGoodsDetails(orderRequest));

        request.setOrderInfo(buildOrderInfo(context));
        request.setRequestType("pay");
        if (AUTH_CAPTURE_MANUAL.equalsIgnoreCase(context.getOrderDomain().getOrderCenterTradeInfo().getCaptureMode())) {
            request.setRequestType(RequestTypeEnum.AUTH.getCode());
        }
        //获取账单，运单  商户，用户信息 (电商专用信息)
        //        buildEcInfo(orderCashierQueryRspDo, payRequestContext, merchantDetailDo);
        // TODO 需要requestType的场景       request.setRequestType(paymentApply.getRequestType());
        request.setQuoteChannelPayCommitNo(context.getPaymentDomain().getChannelPayCommitNo());
        request.setQuoteChannelPayRequestNo(context.getPaymentDomain().getChannelPayRequestNo());
        //卡支付更新支付实例 TODO
        // updatePaymentInstance(paymentInstanceDo, JSONObject.parseObject(paymentApply.getExtParams(), Map.class));
        request.setPaymentMethod(convert.convert2ChannelPaymentMethod(context.getInstrumentDomain().getSelectedPaymentInstance()));
        if (nacosConfig.isPayRemoveDdcOrg() && request.getPaymentMethod() != null) {
            request.setPaymentMethod(transformer.removeExtendProperty(ExtendPropertyEnum.DDC_ORG.getCode(), request.getPaymentMethod()));
        }
        //标准化比对-新收银产品信息
        PayRequestContext.NewPaymentMethod newPaymentMethod = buildNewPaymentMethod(context.getInstrumentDomain().getSelectedInstanceMatchSignMethodInfoDTO(),
                context.getInstrumentDomain().getSelectedPaymentInstance());
        if (Objects.nonNull(newPaymentMethod)) {
            request.setNewPaymentMethod(newPaymentMethod);
        }

        request.setParams(context.getInstrumentDomain().getParams()); // TODO 如果是GPAY的transferDate字段,change format from "yyyyMMdd" to "yyyy-MM-dd"
        request.setAuthorizeCode(context.getInstrumentDomain().getAuthorizeCode());
        PayRequestContext.RiskInfo riskInfo = JSONObject.parseObject(context.getRiskDomain().getRiskInfo(), PayRequestContext.RiskInfo.class);
        riskInfo = riskInfo == null ? new PayRequestContext.RiskInfo() : riskInfo;
        request.setRiskInfo(riskInfo);
        request.setThreeDomainSecureToken(RiskContextRemoteService.cashierModeRiskContextKey(context.getPaymentDomain().getPayCountry(), context.getPaymentDomain().getTradeToken()));
        request.setPaymentIntegration(PaymentIntegrationEnum.CASHIER_CHECKOUT.name());
        request.setAsyncPaySourceIp(IpUtils.getServerIp());

        // 构建PaymentRequestContext
        PaymentRequestContext paymentRequestContext = new PaymentRequestContext();
        paymentRequestContext.setPayRequestContextInfo(request);
        paymentRequestContext.setPaymentInstance(context.getInstrumentDomain().getSelectedPaymentInstance());
        if (nacosConfig.isPayRemoveDdcOrg() && paymentRequestContext.getPaymentInstance() != null) {
            paymentRequestContext.setPaymentInstance(transformer.removeExtendProperty(ExtendPropertyEnum.DDC_ORG.getCode(), paymentRequestContext.getPaymentInstance()));
        }

        // 构建PaymentContextInfo
        PaymentContextInfo paymentContextInfo = new PaymentContextInfo();
        paymentContextInfo.setPayRequestInfo(paymentRequestContext);
        return paymentContextInfo;
    }

    public PaymentContextSaveRequestDTO toPaymentContextSaveRequestDTO(PaymentDomainContext context, Long timeOut) {
        PaymentContextSaveRequestDTO paymentContextSaveRequest = new PaymentContextSaveRequestDTO();
        paymentContextSaveRequest.setIsUpdateExpireTime(true);
        paymentContextSaveRequest.setTimeOut(timeOut);
        paymentContextSaveRequest.setType(PaymentContextTypeEnum.PAYMENT_REQUEST);
        paymentContextSaveRequest.setRequestNo(context.getPaymentDomain().getPayRequestNo());
        return paymentContextSaveRequest;
    }


    private List<PayRequestContext.GoodsDetail> buildGoodsDetails(TradeOrderRequest orderRequest) {
        if (Objects.nonNull(orderRequest)) {
            return goodsDetailPreservativeConvert.convert2GoodsDetail(orderRequest.getGoodsDetails());
        }
        return null;
    }

    public PayRequestContext.ShippingInfo buildShipping(TradeOrderRequest orderRequest) {
        PayRequestContext.ShippingInfo shippingInfo = null;
        if (Objects.nonNull(orderRequest) && Objects.nonNull(orderRequest.getShippingInfo())) {
            TradeOrderRequest.ShippingInfo voucherShippingInfo = orderRequest.getShippingInfo();
            shippingInfo = new PayRequestContext.ShippingInfo();
            shippingInfo.setShippingFirstName(voucherShippingInfo.getFirstName());
            shippingInfo.setShippingLastName(voucherShippingInfo.getLastName());
            shippingInfo.setShippingAddress(voucherShippingInfo.getAddress1());
            shippingInfo.setShippingAddress2(voucherShippingInfo.getAddress2());
            shippingInfo.setShippingAddress3(voucherShippingInfo.getAddress3());
            shippingInfo.setShippingCity(voucherShippingInfo.getCity());
            shippingInfo.setShippingRegion(voucherShippingInfo.getRegion());
            shippingInfo.setShippingState(voucherShippingInfo.getState());
            shippingInfo.setShippingCountry(voucherShippingInfo.getCountry());
            shippingInfo.setShippingZip(voucherShippingInfo.getZipCode());
            shippingInfo.setShippingPhone(voucherShippingInfo.getPhoneNo());
        }
        return shippingInfo;
    }

    public PayRequestContext.BillingInfo buildBilling(TradeOrderRequest orderRequest) {
        PayRequestContext.BillingInfo billingInfo = null;
        if (Objects.nonNull(orderRequest) && Objects.nonNull(orderRequest.getBillingInfo())) {
            TradeOrderRequest.BillingInfo voucherBillingInfo = orderRequest.getBillingInfo();
            billingInfo = new PayRequestContext.BillingInfo();
            billingInfo.setBillingFirstName(voucherBillingInfo.getFirstName());
            billingInfo.setBillingLastName(voucherBillingInfo.getLastName());
            billingInfo.setBillingAddress(voucherBillingInfo.getAddress1());
            billingInfo.setBillingAddress2(voucherBillingInfo.getAddress2());
            billingInfo.setBillingAddress3(voucherBillingInfo.getAddress3());
            billingInfo.setBillingCity(voucherBillingInfo.getCity());
            billingInfo.setBillingRegion(voucherBillingInfo.getRegion());
            billingInfo.setBillingState(voucherBillingInfo.getState());
            billingInfo.setBillingCountry(voucherBillingInfo.getCountry());
            billingInfo.setBillingZip(voucherBillingInfo.getZipCode());
            billingInfo.setBillingPhone(voucherBillingInfo.getPhoneNo());
        }
        return billingInfo;
    }

    public PayRequestContext.MerchantInfo buildMerchantInfo(PaymentDomainContext context) {
        PayRequestContext.MerchantInfo merchantInfo = new PayRequestContext.MerchantInfo();
        merchantInfo.setMerchantNo(context.getMerchantDomain().getMerchantId());
        merchantInfo.setMerchantName(context.getMerchantDomain().getSimpleName());
        merchantInfo.setMerchantCountry(context.getMerchantDomain().getMerchantCountry());
        merchantInfo.setMerchantStreet(context.getMerchantDomain().getRegisterAddress());
        return merchantInfo;
    }

    public PayRequestContext.OutUserInfo buildOutUserInfo(TradeOrderRequest orderRequest) {
        PayRequestContext.OutUserInfo outUserInfo = new PayRequestContext.OutUserInfo();
        if (Objects.nonNull(orderRequest) && Objects.nonNull(orderRequest.getPaymentDetail())) {
            TradeOrderRequest.OutUserInfo buyerInfo = orderRequest.getPaymentDetail().getBuyerInfo();
            outUserInfo = new PayRequestContext.OutUserInfo();
            outUserInfo.setUserEmail(Objects.nonNull(buyerInfo)?buyerInfo.getEmail():StringUtils.EMPTY);
        }
        return outUserInfo;
    }

    public PayRequestContext.OrderInfo buildOrderInfo(PaymentDomainContext context) {
        PayRequestContext.OrderInfo orderInfo = new PayRequestContext.OrderInfo();
        //获取风控信息
        Map<String, String> riskMap = PaymentUtils.strToMap(context.getRiskDomain().getRiskInfo());
        orderInfo.setMerchantNo(context.getMerchantDomain().getMerchantId());
        orderInfo.setMcc(context.getMerchantDomain().getMcc());
        SubMerchantBaseInfo subMerchantBaseInfo = Optional.ofNullable(context.getMerchantDomain().getContract()).map(ContractSignResp::getSubMerchantBaseInfo).orElse(null);
        orderInfo.setSubMerchant(subMerchantConvert.convertSubMerchantBaseInfoForPayRequest(subMerchantBaseInfo));
        orderInfo.setMerchantType(context.getMerchantDomain().getNewMerchantType());
        orderInfo.setOutUserId(context.getOrderDomain().getOrderCenterTradeInfo().getOutUserId());
        orderInfo.setPurchaseInfo(context.getOrderDomain().getOrderCenterTradeInfo().getSubject());
        orderInfo.setRemark(context.getOrderDomain().getOrderCenterTradeInfo().getSubject());
        orderInfo.setUserMemberId(context.getOrderDomain().getOrderCenterTradeInfo().getUserMemberId());
        orderInfo.setUserLanguage(context.getDeviceDomain().getLanguage());
        PayRequestContext.OrderInfo.EnvInfo envInfo = new PayRequestContext.OrderInfo.EnvInfo();
        envInfo.setClientIp(context.getDeviceDomain().getRequestIp());
        envInfo.setTerminalType(context.getDeviceDomain().getType().name());
        envInfo.setOsType(context.getDeviceDomain().getOsType());
        envInfo.setAcceptHeader(StrUtilsExt.defaultString(riskMap.get(PaymentCommon.ACCEPT_HEADER), StringUtils.EMPTY));
        envInfo.setUserAgentHeader(StrUtilsExt.defaultString(riskMap.get(PaymentCommon.USER_AGENT), StringUtils.EMPTY));
        envInfo.setBrowserLanguage(StrUtilsExt.defaultString(riskMap.get(PaymentCommon.BROWSER_LANGUAGE), StringUtils.EMPTY));
        envInfo.setBrowserColorDepth(StrUtilsExt.objectToString(riskMap.get(PaymentCommon.BROWSER_COLOR_DEPTH), StringUtils.EMPTY));
        envInfo.setBrowserTZ(StrUtilsExt.objectToString(riskMap.get(PaymentCommon.BROWSER_TZ), StringUtils.EMPTY));
        envInfo.setBrowserJavaEnabled(StrUtilsExt.objectToString(riskMap.get(PaymentCommon.BROWSER_JAVA_ENABLE), StringUtils.EMPTY));
        envInfo.setBrowserScreenWidth(StrUtilsExt.objectToString(riskMap.get(PaymentCommon.BROWSER_SCREEN_WIDTH), StringUtils.EMPTY));
        envInfo.setBrowserScreenHeight(StrUtilsExt.objectToString(riskMap.get(PaymentCommon.BROWSER_SCREEN_HEIGHT), StringUtils.EMPTY));
        orderInfo.setEnvInfo(envInfo);
        orderInfo.setRealCardBin(context.getInstrumentDomain().getRealCardBin());
//        orderInfo.setCardIssueBank(paymentApply.getIssuer()); // TODO
        orderInfo.setShopId(context.getOrderDomain().getOrderCenterTradeInfo().getShopId());
        orderInfo.setSubMcc(context.getMerchantDomain().getSubMccId());
        orderInfo.setCustomId(context.getMerchantDomain().getCustomId());
        orderInfo.setIntegrate(context.getOrderDomain().getOrderCenterTradeInfo().getIntegrate());
        orderInfo.setIsOpenCbWarning(context.getMerchantDomain().getOpenCbWarning());
        orderInfo.setAuthorizationType(context.getOrderDomain().getOrderCenterTradeInfo().getAuthorizationType());
        orderInfo.setCaptureMode(context.getOrderDomain().getOrderCenterTradeInfo().getCaptureMode());
        return orderInfo;
    }

    /**
     * 标准化比对-新收银产品信息
     */
    public PayRequestContext.NewPaymentMethod buildNewPaymentMethod(SignMethodInfoDTO signMethodInfoDTO, PaymentInstance paymentInstance){
        try {
            if (Objects.nonNull(signMethodInfoDTO) && signMethodInfoDTO.getCompareFlag() && CollUtil.isNotEmpty(signMethodInfoDTO.getPaymentMethodPairInfoDTOList())) {
                SignMethodInfoDTO.PaymentMethodPairInfoDTO pairInfoDTO = null;
                List<SignMethodInfoDTO.PaymentMethodPairInfoDTO> paymentMethodPairInfoDTOList = signMethodInfoDTO.getPaymentMethodPairInfoDTOList();
                if (CollUtil.size(paymentMethodPairInfoDTOList) == CommonConstants.NUMBER_ONE) {
                    pairInfoDTO = paymentMethodPairInfoDTOList.get(0);//CHECKED
                } else {
                    // 一拆多情况下按目标机构或卡组匹配
                    List<SignMethodInfoDTO.PaymentMethodPairInfoDTO> matchPairInfoDTOList = paymentMethodPairInfoDTOList.stream()
                            .map(p -> this.filterMatchPairInfo(p, paymentInstance.getTargetOrg(), paymentInstance.getCardOrg()))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    if (CollUtil.isEmpty(matchPairInfoDTOList) || CollUtil.size(matchPairInfoDTOList) > CommonConstants.NUMBER_ONE) {
                        log.error("get pairInfoDTO from matchPairInfoDTOList error, matchPairInfoDTOList is empty or size > 1, targetOrg:{}, cardOrg:{}, matchPairInfoDTOList:{}",
                                paymentInstance.getTargetOrg(), paymentInstance.getCardOrg(), JSON.toJSONString(matchPairInfoDTOList));

                    } else {
                        pairInfoDTO = matchPairInfoDTOList.get(0);//CHECKED
                    }
                }
                if (Objects.nonNull(pairInfoDTO) && StrUtil.isNotBlank(pairInfoDTO.getCashierProductNo()) && StrUtil.isNotBlank(pairInfoDTO.getPaymentMode())) {
                    PayRequestContext.NewPaymentMethod newPaymentMethod = new PayRequestContext.NewPaymentMethod();
                    newPaymentMethod.setCashierProductNo(pairInfoDTO.getCashierProductNo());
                    newPaymentMethod.setPaymentMethodType(pairInfoDTO.getPaymentMode());
                    // 这里卡、ApplePay、GooglePay等支付方式的targetOrg都为null，需要转换为*
                    newPaymentMethod.setTargetOrg(StrUtil.nullToDefault(pairInfoDTO.getTargetOrg(), CommonConstants.ASTERISK));
                    return newPaymentMethod;
                }
            }
            return null;
        } catch (Exception e) {
            log.error("buildNewPaymentMethod catch exception", e);
            return null;
        }
    }

    private SignMethodInfoDTO.PaymentMethodPairInfoDTO filterMatchPairInfo(SignMethodInfoDTO.PaymentMethodPairInfoDTO pairInfo, String targetOrg,String cardOrg){
        if (StrUtil.isEmpty(targetOrg) || StrUtil.equals(targetOrg, CommonConstants.ASTERISK)) {
            if (CollUtil.contains(pairInfo.getCardOrgList(), cardOrg)) {
                return pairInfo;
            } else {
                return null;
            }
        } else if (StrUtil.equals(targetOrg, pairInfo.getTargetOrg())) {
            return pairInfo;
        } else {
            return null;
        }
    }
}
